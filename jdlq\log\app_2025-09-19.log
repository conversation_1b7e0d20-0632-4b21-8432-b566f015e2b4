读取环境变量立即执行优惠券为：4-4
加载API:话费
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：1,3,1,20,20,380

2025-09-19 09:05:31:267:----脚本运行成功，请不要关闭窗口----

2025-09-19 09:05:31:268:----开始运行脚本----
共检测到1个cookie
下次抢券时间：10:00:00
名称：话费
立即抢券（跑完记得删除或禁用该环境变量）：4-4


1、2025-09-19 09:05:31:270:开始领取4-4_账号1
54分后才开始！


*4-4_【账号1】1907992-14820856*
2025-09-19 09:05:31:611:{"subCodeMsg":"您来早了，下一场活动开始时间为 10:00，稍后再来吧！","subCode":"A8","code":"0","msg":null}
未配置自定义API！
读取环境变量成功：2,3,3,20,20,380

2025-09-19 09:59:06:399:----脚本运行成功，请不要关闭窗口----

2025-09-19 09:59:06:400:----开始运行脚本----
共检测到3个cookie
下次抢券时间：10:00:00
名称：话费
名称：4-4
0分后开始任务，请不要结束任务！
未配置自定义API！
读取环境变量成功：2,3,3,20,20,380

2025-09-19 09:59:14:530:----脚本运行成功，请不要关闭窗口----

2025-09-19 09:59:14:530:----开始运行脚本----
共检测到3个cookie
下次抢券时间：10:00:00
名称：话费
0分后开始任务，请不要结束任务！


***开始领券【话费】第1次请求***


1、2025-09-19 09:59:59:641:开始领取话费_账号1


2、2025-09-19 09:59:59:692:开始领取话费_账号2


3、2025-09-19 09:59:59:799:开始领取话费_账号3


***开始领券【话费】第2次请求***


4、2025-09-19 09:59:59:820:开始领取话费_账号1


5、2025-09-19 09:59:59:851:开始领取话费_账号2


6、2025-09-19 09:59:59:859:开始领取话费_账号3


*话费_【账号3】jd_414c6286ab55d*
2025-09-19 09:59:59:976:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号2】jd_614aa177acd06*
2025-09-19 09:59:59:977:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号1】1907992-14820856*
2025-09-19 09:59:59:978:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号1】1907992-14820856*
2025-09-19 09:59:59:986:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号2】jd_614aa177acd06*
2025-09-19 10:00:00:16:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号3】jd_414c6286ab55d*
2025-09-19 10:00:00:19:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}
读取环境变量立即执行优惠券为：4-4
加载API:话费
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：1,3,1,20,20,380

2025-09-19 10:01:17:54:----脚本运行成功，请不要关闭窗口----

2025-09-19 10:01:17:55:----开始运行脚本----
共检测到6个cookie
下次抢券时间：11:00:00
立即抢券（跑完记得删除或禁用该环境变量）：4-4


1、2025-09-19 10:01:17:57:开始领取4-4_账号1
58分后才开始！


2、2025-09-19 10:01:17:197:开始领取4-4_账号2


3、2025-09-19 10:01:17:217:开始领取4-4_账号3


4、2025-09-19 10:01:17:247:开始领取4-4_账号4


5、2025-09-19 10:01:17:278:开始领取4-4_账号5


6、2025-09-19 10:01:17:308:开始领取4-4_账号6


*4-4_【账号4】jd_614aa177acd06*
2025-09-19 10:01:17:427:本时段优惠券已抢完，请12:00再来吧！


*4-4_【账号6】jd_51b29ca7e9a2b*
2025-09-19 10:01:17:460:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*4-4_【账号1】zhq115*
2025-09-19 10:01:17:615:本时段优惠券已抢完，请12:00再来吧！
加载API:话费
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：2,3,6,100,20,380

2025-09-19 11:55:53:637:----脚本运行成功，请不要关闭窗口----

2025-09-19 11:55:53:637:----开始运行脚本----
共检测到6个cookie
下次抢券时间：12:00:00
名称：4-4
4分后才开始！
加载API:话费
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：2,3,6,100,20,380

2025-09-19 11:58:21:0:----脚本运行成功，请不要关闭窗口----

2025-09-19 11:58:21:1:----开始运行脚本----
共检测到6个cookie
下次抢券时间：12:00:00
名称：4-4
1分后开始任务，请不要结束任务！
任务执行中...


***开始领券【4-4】第1次请求***


1、2025-09-19 11:59:59:638:开始领取4-4_账号1


2、2025-09-19 11:59:59:678:开始领取4-4_账号2


3、2025-09-19 11:59:59:690:开始领取4-4_账号3


4、2025-09-19 11:59:59:698:开始领取4-4_账号4


5、2025-09-19 11:59:59:703:开始领取4-4_账号5


6、2025-09-19 11:59:59:709:开始领取4-4_账号6


***开始领券【4-4】第2次请求***


7、2025-09-19 11:59:59:820:开始领取4-4_账号1


8、2025-09-19 11:59:59:837:开始领取4-4_账号2


9、2025-09-19 11:59:59:853:开始领取4-4_账号3


10、2025-09-19 11:59:59:884:开始领取4-4_账号4


11、2025-09-19 11:59:59:915:开始领取4-4_账号5


12、2025-09-19 11:59:59:931:开始领取4-4_账号6


*4-4_【账号2】jd_UpJHStdVIoZe*
2025-09-19 12:00:00:32:领取成功！感谢您的参与，祝您购物愉快~,subCode2_|A1|


*4-4_【账号4】jd_614aa177acd06*
2025-09-19 12:00:00:79:本时段优惠券已抢完，请14:00再来吧！

************************


券【4-4】成功领取的用户有：
2、jd_UpJHStdVIoZe


*4-4_【账号3】haiquan1212*
2025-09-19 12:00:02:998:本时段优惠券已抢完，请14:00再来吧！


*4-4_【账号5】1907992-14820856*
2025-09-19 12:00:03:115:本时段优惠券已抢完，请14:00再来吧！
API请求失败，请检查网络重试
API请求失败，请检查网络重试
API请求失败，请检查网络重试
API请求失败，请检查网络重试
API请求失败，请检查网络重试
API请求失败，请检查网络重试
API请求失败，请检查网络重试
API请求失败，请检查网络重试
加载API:话费
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：2,3,3,20,20,380

2025-09-19 14:33:11:86:----脚本运行成功，请不要关闭窗口----

2025-09-19 14:33:11:87:----开始运行脚本----
共检测到3个cookie
下次抢券时间：15:00:00
名称：话费
26分后才开始！
加载API:话费
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：2,3,3,20,20,380

2025-09-19 14:59:06:20:----脚本运行成功，请不要关闭窗口----

2025-09-19 14:59:06:21:----开始运行脚本----
共检测到3个cookie
下次抢券时间：15:00:00
名称：话费
0分后开始任务，请不要结束任务！
加载API:话费
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：2,3,3,20,20,380

2025-09-19 14:59:10:912:----脚本运行成功，请不要关闭窗口----

2025-09-19 14:59:10:913:----开始运行脚本----
共检测到3个cookie
下次抢券时间：15:00:00
名称：话费
0分后开始任务，请不要结束任务！


***开始领券【话费】第1次请求***


1、2025-09-19 14:59:59:628:开始领取话费_账号1


2、2025-09-19 14:59:59:682:开始领取话费_账号2


3、2025-09-19 14:59:59:691:开始领取话费_账号3


***开始领券【话费】第2次请求***


4、2025-09-19 14:59:59:825:开始领取话费_账号1


5、2025-09-19 14:59:59:856:开始领取话费_账号2


6、2025-09-19 14:59:59:887:开始领取话费_账号3


*话费_【账号1】1907992-14820856*
2025-09-19 14:59:59:992:时间未到继续：本时段优惠券已抢完，请15:00再来吧！


*话费_【账号2】jd_614aa177acd06*
2025-09-19 14:59:59:993:时间未到继续：本时段优惠券已抢完，请15:00再来吧！


*话费_【账号1】1907992-14820856*
2025-09-19 14:59:59:994:时间未到继续：本时段优惠券已抢完，请15:00再来吧！


*话费_【账号2】jd_614aa177acd06*
2025-09-19 15:00:00:45:本时段优惠券已抢完，请18:00再来吧！


*话费_【账号3】jd_414c6286ab55d*
2025-09-19 15:00:00:48:本时段优惠券已抢完，请18:00再来吧！


*话费_【账号3】jd_414c6286ab55d*
2025-09-19 15:00:01:59:本时段优惠券已抢完，请18:00再来吧！

2025-09-19 15:05:00:31:未到任务执行时间，跳过执行......
加载API:话费
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：3,3,3,10,20,380

2025-09-19 17:59:32:626:----脚本运行成功，请不要关闭窗口----

2025-09-19 17:59:32:627:----开始运行脚本----
共检测到3个cookie
下次抢券时间：18:00:00
名称：话费
名称：4-4
0分后开始任务，请不要结束任务！
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：3,3,3,10,20,380

2025-09-19 17:59:40:841:----脚本运行成功，请不要关闭窗口----

2025-09-19 17:59:40:841:----开始运行脚本----
共检测到3个cookie
下次抢券时间：18:00:00
名称：话费
0分后开始任务，请不要结束任务！


***开始领券【话费】第1次请求***


1、2025-09-19 17:59:59:629:开始领取话费_账号1


2、2025-09-19 17:59:59:669:开始领取话费_账号2


3、2025-09-19 17:59:59:679:开始领取话费_账号3


***开始领券【话费】第2次请求***


4、2025-09-19 17:59:59:801:开始领取话费_账号1


5、2025-09-19 17:59:59:829:开始领取话费_账号2


6、2025-09-19 17:59:59:860:开始领取话费_账号3


***开始领券【话费】第3次请求***


7、2025-09-19 17:59:59:871:开始领取话费_账号1


8、2025-09-19 17:59:59:891:开始领取话费_账号2


9、2025-09-19 17:59:59:908:开始领取话费_账号3


*话费_【账号1】1907992-14820856*
2025-09-19 17:59:59:983:时间未到继续：本时段优惠券已抢完，请18:00再来吧！


*话费_【账号2】jd_614aa177acd06*
2025-09-19 17:59:59:985:时间未到继续：本时段优惠券已抢完，请18:00再来吧！


*话费_【账号3】jd_414c6286ab55d*
2025-09-19 17:59:59:985:时间未到继续：本时段优惠券已抢完，请18:00再来吧！


*话费_【账号1】1907992-14820856*
2025-09-19 17:59:59:986:时间未到继续：本时段优惠券已抢完，请18:00再来吧！


*话费_【账号2】jd_614aa177acd06*
2025-09-19 17:59:59:991:时间未到继续：本时段优惠券已抢完，请18:00再来吧！


*话费_【账号3】jd_414c6286ab55d*
2025-09-19 18:00:00:51:本时段优惠券已抢完，请20:00再来吧！


*话费_【账号2】jd_614aa177acd06*
2025-09-19 18:00:00:58:本时段优惠券已抢完，请20:00再来吧！


*话费_【账号1】1907992-14820856*
2025-09-19 18:00:00:63:本时段优惠券已抢完，请20:00再来吧！


*话费_【账号3】jd_414c6286ab55d*
2025-09-19 18:00:00:71:本时段优惠券已抢完，请20:00再来吧！
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：3,3,3,10,20,370

2025-09-19 19:59:16:374:----脚本运行成功，请不要关闭窗口----

2025-09-19 19:59:16:374:----开始运行脚本----
共检测到3个cookie
下次抢券时间：20:00:00
名称：话费
0分后开始任务，请不要结束任务！


***开始领券【话费】第1次请求***


1、2025-09-19 19:59:59:639:开始领取话费_账号1


2、2025-09-19 19:59:59:681:开始领取话费_账号2


3、2025-09-19 19:59:59:696:开始领取话费_账号3


***开始领券【话费】第2次请求***


4、2025-09-19 19:59:59:826:开始领取话费_账号1


5、2025-09-19 19:59:59:852:开始领取话费_账号2


6、2025-09-19 19:59:59:882:开始领取话费_账号3


***开始领券【话费】第3次请求***


7、2025-09-19 19:59:59:897:开始领取话费_账号1


8、2025-09-19 19:59:59:924:开始领取话费_账号2


9、2025-09-19 19:59:59:943:开始领取话费_账号3


*话费_【账号2】jd_614aa177acd06*
2025-09-19 20:00:00:46:本时段优惠券已抢完，请22:00再来吧！


*话费_【账号1】1907992-14820856*
2025-09-19 20:00:00:52:本时段优惠券已抢完，请22:00再来吧！


*话费_【账号2】jd_614aa177acd06*
2025-09-19 20:00:00:55:本时段优惠券已抢完，请22:00再来吧！


*话费_【账号3】jd_414c6286ab55d*
2025-09-19 20:00:00:79:本时段优惠券已抢完，请22:00再来吧！


*话费_【账号3】jd_414c6286ab55d*
2025-09-19 20:00:00:87:本时段优惠券已抢完，请22:00再来吧！


*话费_【账号1】1907992-14820856*
2025-09-19 20:00:00:93:本时段优惠券已抢完，请22:00再来吧！


*话费_【账号2】jd_614aa177acd06*
2025-09-19 20:00:00:96:本时段优惠券已抢完，请22:00再来吧！


*话费_【账号3】jd_414c6286ab55d*
2025-09-19 20:00:00:122:本时段优惠券已抢完，请22:00再来吧！


*话费_【账号1】1907992-14820856*
2025-09-19 20:00:00:470:本时段优惠券已抢完，请22:00再来吧！

2025-09-19 20:05:00:24:未到任务执行时间，跳过执行......

2025-09-19 20:10:00:18:未到任务执行时间，跳过执行......

2025-09-19 20:15:00:30:未到任务执行时间，跳过执行......

2025-09-19 20:20:00:26:未到任务执行时间，跳过执行......

2025-09-19 20:25:00:25:未到任务执行时间，跳过执行......

2025-09-19 20:30:00:29:未到任务执行时间，跳过执行......

2025-09-19 20:35:00:21:未到任务执行时间，跳过执行......

2025-09-19 20:40:00:10:未到任务执行时间，跳过执行......

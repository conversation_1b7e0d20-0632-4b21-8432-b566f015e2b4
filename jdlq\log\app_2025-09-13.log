读取环境变量立即执行优惠券为：4-4
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：1,3,1,20,20,200

2025-09-13 13:38:17:257:----脚本运行成功，请不要关闭窗口----

2025-09-13 13:38:17:258:----开始运行脚本----
共检测到1个cookie
下次抢券时间：14:00:00
立即抢券（跑完记得删除或禁用该环境变量）：4-4


1、2025-09-13 13:38:17:260:开始领取4-4_账号1
21分后才开始！


*4-4_【账号1】JRSD_YFlLe0305*
2025-09-13 13:38:17:569:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}
读取环境变量立即执行优惠券为：4-4
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：1,3,1,20,20,200

2025-09-13 13:38:41:611:----脚本运行成功，请不要关闭窗口----

2025-09-13 13:38:41:612:----开始运行脚本----
共检测到1个cookie
下次抢券时间：14:00:00
立即抢券（跑完记得删除或禁用该环境变量）：4-4


1、2025-09-13 13:38:41:614:开始领取4-4_账号1
21分后才开始！


*4-4_【账号1】1907992-14820856*
2025-09-13 13:38:41:911:本时段优惠券已抢完，请15:00再来吧！
读取环境变量立即执行优惠券为：ceshi
加载API:ceshi
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：1,3,1,20,20,200

2025-09-13 13:39:12:760:----脚本运行成功，请不要关闭窗口----

2025-09-13 13:39:12:761:----开始运行脚本----
共检测到1个cookie
下次抢券时间：14:00:00
立即抢券（跑完记得删除或禁用该环境变量）：ceshi


1、2025-09-13 13:39:12:763:开始领取ceshi_账号1
20分后才开始！


*ceshi_【账号1】1907992-14820856*
2025-09-13 13:39:13:48:{"subCodeMsg":"没抢到，请刷新页面重试~~","subCode":"A1001","code":"0","msg":null}
读取环境变量立即执行优惠券为：ceshi
加载API:ceshi
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：1,3,1,20,20,200

2025-09-13 13:39:47:946:----脚本运行成功，请不要关闭窗口----

2025-09-13 13:39:47:947:----开始运行脚本----
共检测到1个cookie
下次抢券时间：14:00:00
立即抢券（跑完记得删除或禁用该环境变量）：ceshi


1、2025-09-13 13:39:47:949:开始领取ceshi_账号1
20分后才开始！


*ceshi_【账号1】1907992-14820856*
2025-09-13 13:39:48:243:您来太晚了，活动已经结束了哟~
读取环境变量立即执行优惠券为：4-4
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：1,3,1,20,20,200

2025-09-13 13:42:20:960:----脚本运行成功，请不要关闭窗口----

2025-09-13 13:42:20:961:----开始运行脚本----
共检测到1个cookie
下次抢券时间：14:00:00
立即抢券（跑完记得删除或禁用该环境变量）：4-4


1、2025-09-13 13:42:20:965:开始领取4-4_账号1
17分后才开始！


*4-4_【账号1】1907992-14820856*
2025-09-13 13:42:21:306:时间未到继续：本时段优惠券已抢完，请14:00再来吧！
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：1,3,1,20,20,100

2025-09-13 13:55:46:780:----脚本运行成功，请不要关闭窗口----

2025-09-13 13:55:46:781:----开始运行脚本----
共检测到5个cookie
下次抢券时间：14:00:00
名称：4-4
4分后才开始！

2025-09-13 13:59:00:21:----开始运行脚本----
共检测到5个cookie
下次抢券时间：14:00:00
名称：4-4
0分后开始任务，请不要结束任务！


***开始领券【4-4】第1次请求***


1、2025-09-13 13:59:59:912:开始领取4-4_账号1


2、2025-09-13 14:00:00:45:开始领取4-4_账号2


3、2025-09-13 14:00:00:78:开始领取4-4_账号3


4、2025-09-13 14:00:00:109:开始领取4-4_账号4


5、2025-09-13 14:00:00:141:开始领取4-4_账号5


*4-4_【账号1】zhq115*
2025-09-13 14:00:00:242:领取成功！感谢您的参与，祝您购物愉快~,subCode2_|A1|


*4-4_【账号2】jd_UpJHStdVIoZe*
2025-09-13 14:00:00:243:领取成功！感谢您的参与，祝您购物愉快~,subCode2_|A1|


*4-4_【账号3】haiquan1212*
2025-09-13 14:00:00:289:领取成功！感谢您的参与，祝您购物愉快~,subCode2_|A1|


*4-4_【账号5】wang1131253913*
2025-09-13 14:00:00:319:领取成功！感谢您的参与，祝您购物愉快~,subCode2_|A1|


*4-4_【账号4】jd_614aa177acd06*
2025-09-13 14:00:00:333:领取成功！感谢您的参与，祝您购物愉快~,subCode2_|A1|

************************


券【4-4】成功领取的用户有：
1、zhq115
2、jd_UpJHStdVIoZe
3、haiquan1212
5、wang1131253913
4、jd_614aa177acd06
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：2,3,1,20,20,380

2025-09-13 17:59:10:865:----脚本运行成功，请不要关闭窗口----

2025-09-13 17:59:10:866:----开始运行脚本----
共检测到1个cookie
下次抢券时间：18:00:00
名称：话费
0分后开始任务，请不要结束任务！


***开始领券【话费】第1次请求***


1、2025-09-13 17:59:59:631:开始领取话费_账号1


***开始领券【话费】第2次请求***


2、2025-09-13 17:59:59:769:开始领取话费_账号1


*话费_【账号1】zhq115*
2025-09-13 17:59:59:933:{"subCodeMsg":"没抢到，请刷新页面重试~~","subCode":"A1001","code":"0","msg":null}


*话费_【账号1】zhq115*
2025-09-13 17:59:59:934:{"subCodeMsg":"没抢到，请刷新页面重试~~","subCode":"A1001","code":"0","msg":null}
读取环境变量立即执行优惠券为：话费
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：1,3,1,20,20,380

2025-09-13 18:00:53:558:----脚本运行成功，请不要关闭窗口----

2025-09-13 18:00:53:559:----开始运行脚本----
共检测到1个cookie
下次抢券时间：19:00:00
立即抢券（跑完记得删除或禁用该环境变量）：话费


1、2025-09-13 18:00:53:562:开始领取话费_账号1
59分后才开始！


*话费_【账号1】zhq115*
2025-09-13 18:00:53:857:本时段优惠券已抢完，请20:00再来吧！

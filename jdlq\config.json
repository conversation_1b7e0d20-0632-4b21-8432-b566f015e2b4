{"YHQ_QL_SIGN": "dbs1fkla11i8d", "JD_COOKIE": ["pt_key=AAJoULcaADDDjEs8MXqAoPFOt__YNxVPW_rI81inkv-jT7_minVlY6Uc4x9kPhGuAtZIcqcS4LA; pt_pin=1907992-14820856;", "pt_key=AAJoULZLADCPLD641zIgFXaTQfwOMCf6wTo5GFKwBBYlif2-5bdICRRutduFF90r8-ByhdkipGE; pt_pin=jd_62a1f82c3247f;", "pt_key=AAJoULbYADCE2rX0Bn-tqdXis2IgC09vCcGGxGxiVgKG9ZJb-FWSwTXf1fvFmeIcgNi9gkPp9DE; pt_pin=jd_47cccc568ace6;", "pt_key=AAJoUUm1ADCNpAhrUtPQpji5_jbuJ2wosRA-kIOVXW406cXVpYFSCetkx9eQG7H33CpEitVgtok; pt_pin=JRSD_YFlLe0305;", "pt_key=AAJoXRoSADCkZDKNm0I5dthOLMElM3-4cQlM27yNiDgH7izmkVM7t6QK06B_GlQj8MoplNaRwWo; pt_pin=armishi;"], "YHQ_API": "1,3,5,100,20,240", "YHQ_NOWRUN": "", "YHQ_REMOVE": "", "注释说明": {"YHQ_QL_SIGN": "填写你的激活码", "JD_COOKIE": "账号的 Cookie 列表，每个 Cookie 对应一个账号，格式: pt_key=...; pt_pin=...; 多个复制一行 用逗号隔开 最后一个后面不用加逗号", "YHQ_API": "重试次数,整点抢几种类型券,最大线程数,抢券间隔,默认抢前几个账号的券,提前多少毫秒'", "YHQ_NOWRUN": "立即执行的优惠券名称，默认为空即可 jdYhqApiList.js中qName 要一样且只能一个 跑完切记删除或禁用该环境变量 要不每次都跑", "YHQ_REMOVE": "排除的优惠券名称，默认为空即可  jdYhqApiList.js中qName 不要双引号多个用英文逗号隔开 值设置为all则不抢作者的券仅抢自定义券 例如： 极速10-2,极速15-8"}}
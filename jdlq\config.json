{"YHQ_QL_SIGN": "dbs1fkla11i8d", "JD_COOKIE": ["pt_key=AAJoTsq4ADCN6ewZMcjZ06ByhZXLdOn1HPFVUtKKCJVzL8-3gc7Xc4S-Ht69CgB221Gz35uSU8o; pt_pin=jd_UpJHStdVIoZe;"], "YHQ_API": "3,3,1,100,20,350", "YHQ_NOWRUN": "", "YHQ_REMOVE": "", "注释说明": {"YHQ_QL_SIGN": "填写你的激活码", "JD_COOKIE": "账号的 Cookie 列表，每个 Cookie 对应一个账号，格式: pt_key=...; pt_pin=...; 多个复制一行 用逗号隔开 最后一个后面不用加逗号", "YHQ_API": "重试次数,整点抢几种类型券,最大线程数,抢券间隔,默认抢前几个账号的券,提前多少毫秒'", "YHQ_NOWRUN": "立即执行的优惠券名称，默认为空即可 jdYhqApiList.js中qName 要一样且只能一个 跑完切记删除或禁用该环境变量 要不每次都跑", "YHQ_REMOVE": "排除的优惠券名称，默认为空即可  jdYhqApiList.js中qName 不要双引号多个用英文逗号隔开 值设置为all则不抢作者的券仅抢自定义券 例如： 极速10-2,极速15-8"}}
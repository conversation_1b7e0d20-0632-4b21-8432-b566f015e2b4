加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：3,3,2,20,20,350

2025-09-24 12:00:02:393:----脚本运行成功，请不要关闭窗口----

2025-09-24 12:00:02:393:----开始运行脚本----
共检测到4个cookie
下次抢券时间：13:00:00
当前时间段没有优惠券需要领取！
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：3,3,2,20,20,350

2025-09-24 13:59:02:110:----脚本运行成功，请不要关闭窗口----

2025-09-24 13:59:02:111:----开始运行脚本----
共检测到4个cookie
下次抢券时间：14:00:00
名称：4-4
0分后开始任务，请不要结束任务！


***开始领券【4-4】第1次请求***


1、2025-09-24 13:59:59:660:开始领取4-4_账号1


2、2025-09-24 13:59:59:707:开始领取4-4_账号2


3、2025-09-24 13:59:59:833:开始领取4-4_账号3


4、2025-09-24 13:59:59:856:开始领取4-4_账号4


***开始领券【4-4】第2次请求***


5、2025-09-24 13:59:59:887:开始领取4-4_账号1


6、2025-09-24 13:59:59:919:开始领取4-4_账号2


7、2025-09-24 13:59:59:949:开始领取4-4_账号3


8、2025-09-24 13:59:59:971:开始领取4-4_账号4


***开始领券【4-4】第3次请求***


9、2025-09-24 13:59:59:997:开始领取4-4_账号1


10、2025-09-24 14:00:00:18:开始领取4-4_账号2


*4-4_【账号1】zhq115*
2025-09-24 14:00:00:31:时间未到继续：本时段优惠券已抢完，请14:00再来吧！


11、2025-09-24 14:00:00:33:开始领取4-4_账号3


*4-4_【账号2】jd_UpJHStdVIoZe*
2025-09-24 14:00:00:42:本时段优惠券已抢完，请16:00再来吧！


*4-4_【账号4】jd_614aa177acd06*
2025-09-24 14:00:00:43:本时段优惠券已抢完，请16:00再来吧！


*4-4_【账号3】haiquan1212*
2025-09-24 14:00:00:45:领取成功！感谢您的参与，祝您购物愉快~,subCode2_|A1|


12、2025-09-24 14:00:00:46:开始领取4-4_账号4
该券已无或已结束！


*4-4_【账号1】zhq115*
2025-09-24 14:00:00:83:本时段优惠券已抢完，请16:00再来吧！


*4-4_【账号2】jd_UpJHStdVIoZe*
2025-09-24 14:00:00:85:本时段优惠券已抢完，请16:00再来吧！


*4-4_【账号3】haiquan1212*
2025-09-24 14:00:00:120:本时段优惠券已抢完，请16:00再来吧！


*4-4_【账号4】jd_614aa177acd06*
2025-09-24 14:00:00:142:本时段优惠券已抢完，请16:00再来吧！


*4-4_【账号1】zhq115*
2025-09-24 14:00:00:176:本时段优惠券已抢完，请16:00再来吧！


*4-4_【账号2】jd_UpJHStdVIoZe*
2025-09-24 14:00:00:178:本时段优惠券已抢完，请16:00再来吧！


*4-4_【账号3】haiquan1212*
2025-09-24 14:00:00:197:本时段优惠券已抢完，请16:00再来吧！

************************


券【4-4】成功领取的用户有：
3、haiquan1212

2025-09-24 14:05:00:20:未到任务执行时间，跳过执行......

2025-09-24 14:10:00:19:未到任务执行时间，跳过执行......

2025-09-24 14:15:00:30:未到任务执行时间，跳过执行......

2025-09-24 14:20:00:28:未到任务执行时间，跳过执行......

2025-09-24 14:25:00:25:未到任务执行时间，跳过执行......

2025-09-24 14:30:00:22:未到任务执行时间，跳过执行......

2025-09-24 14:35:00:36:未到任务执行时间，跳过执行......

2025-09-24 14:40:00:31:未到任务执行时间，跳过执行......

2025-09-24 14:45:00:26:未到任务执行时间，跳过执行......

2025-09-24 14:50:00:32:未到任务执行时间，跳过执行......

2025-09-24 14:55:00:43:未到任务执行时间，跳过执行......

2025-09-24 14:59:00:27:----开始运行脚本----
共检测到4个cookie
下次抢券时间：15:00:00
当前时间段没有优惠券需要领取！
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：3,3,1,100,20,350

2025-09-24 14:59:43:346:----脚本运行成功，请不要关闭窗口----

2025-09-24 14:59:43:347:----开始运行脚本----
共检测到3个cookie
下次抢券时间：15:00:00
当前时间段没有优惠券需要领取！

2025-09-24 15:00:00:25:未到任务执行时间，跳过执行......

2025-09-24 15:00:00:17:未到任务执行时间，跳过执行......
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：3,3,1,100,20,350

2025-09-24 17:59:42:298:----脚本运行成功，请不要关闭窗口----

2025-09-24 17:59:42:298:----开始运行脚本----
共检测到3个cookie
下次抢券时间：18:00:00
当前时间段没有优惠券需要领取！

2025-09-24 18:00:00:27:未到任务执行时间，跳过执行......
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：3,3,1,100,20,350

2025-09-24 19:59:42:135:----脚本运行成功，请不要关闭窗口----

2025-09-24 19:59:42:136:----开始运行脚本----
共检测到2个cookie
下次抢券时间：20:00:00
名称：4-4
0分后开始任务，请不要结束任务！


***开始领券【4-4】第1次请求***


1、2025-09-24 19:59:59:656:开始领取4-4_账号1


2、2025-09-24 19:59:59:803:开始领取4-4_账号2


***开始领券【4-4】第2次请求***


3、2025-09-24 19:59:59:903:开始领取4-4_账号1


*4-4_【账号1】zhq115*
2025-09-24 19:59:59:952:{"subCodeMsg":"您来早了，下一场活动开始时间为 20:00，稍后再来吧！","subCode":"A8","code":"0","msg":null}


*4-4_【账号2】jd_UpJHStdVIoZe*
2025-09-24 19:59:59:995:{"subCodeMsg":"您来早了，下一场活动开始时间为 20:00，稍后再来吧！","subCode":"A8","code":"0","msg":null}


4、2025-09-24 20:00:00:4:开始领取4-4_账号2


***开始领券【4-4】第3次请求***


*4-4_【账号1】zhq115*
2025-09-24 20:00:00:61:此券今日已经被抢完，请您明日再来~


*4-4_【账号2】jd_UpJHStdVIoZe*
2025-09-24 20:00:00:76:此券今日已经被抢完，请您明日再来~


5、2025-09-24 20:00:00:114:开始领取4-4_账号1
该券已无或已结束！
该券已无或者无账号需要请求！
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：3,3,1,100,20,350

2025-09-24 21:59:42:142:----脚本运行成功，请不要关闭窗口----

2025-09-24 21:59:42:143:----开始运行脚本----
共检测到2个cookie
下次抢券时间：22:00:00
当前时间段没有优惠券需要领取！

2025-09-24 22:00:00:25:未到任务执行时间，跳过执行......

加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：3,3,1,100,20,350

2025-09-25 09:59:42:274:----脚本运行成功，请不要关闭窗口----

2025-09-25 09:59:42:275:----开始运行脚本----
共检测到2个cookie
下次抢券时间：10:00:00
名称：4-4
0分后开始任务，请不要结束任务！


***开始领券【4-4】第1次请求***


1、2025-09-25 09:59:59:662:开始领取4-4_账号1


2、2025-09-25 09:59:59:881:开始领取4-4_账号2


***开始领券【4-4】第2次请求***


3、2025-09-25 09:59:59:981:开始领取4-4_账号1


4、2025-09-25 10:00:00:77:开始领取4-4_账号2


***开始领券【4-4】第3次请求***


*4-4_【账号1】zhq115*
2025-09-25 10:00:00:87:领取成功！感谢您的参与，祝您购物愉快~,subCode2_|A1|


*4-4_【账号2】jd_UpJHStdVIoZe*
2025-09-25 10:00:00:89:本时段优惠券已抢完，请12:00再来吧！


*4-4_【账号1】zhq115*
2025-09-25 10:00:00:151:本时段优惠券已抢完，请12:00再来吧！


*4-4_【账号2】jd_UpJHStdVIoZe*
2025-09-25 10:00:00:152:本时段优惠券已抢完，请12:00再来吧！


5、2025-09-25 10:00:00:181:开始领取4-4_账号1
该券已无或已结束！
该券已无或者无账号需要请求！

************************


券【4-4】成功领取的用户有：
1、zhq115
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：3,3,1,100,20,350

2025-09-25 13:59:20:925:----脚本运行成功，请不要关闭窗口----

2025-09-25 13:59:20:926:----开始运行脚本----
共检测到1个cookie
下次抢券时间：14:00:00
名称：4-4
0分后开始任务，请不要结束任务！


***开始领券【4-4】第1次请求***


1、2025-09-25 13:59:59:656:开始领取4-4_账号1


***开始领券【4-4】第2次请求***


2、2025-09-25 13:59:59:804:开始领取4-4_账号1


***开始领券【4-4】第3次请求***


3、2025-09-25 13:59:59:911:开始领取4-4_账号1


*4-4_【账号1】jd_UpJHStdVIoZe*
2025-09-25 13:59:59:981:时间未到继续：本时段优惠券已抢完，请14:00再来吧！


*4-4_【账号1】jd_UpJHStdVIoZe*
2025-09-25 14:00:00:62:本时段优惠券已抢完，请16:00再来吧！


*4-4_【账号1】jd_UpJHStdVIoZe*
2025-09-25 14:00:00:142:本时段优惠券已抢完，请16:00再来吧！

2025-09-25 14:05:00:21:未到任务执行时间，跳过执行......

2025-09-25 14:10:00:30:未到任务执行时间，跳过执行......

2025-09-25 14:15:00:29:未到任务执行时间，跳过执行......

2025-09-25 14:20:00:33:未到任务执行时间，跳过执行......
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：3,3,1,100,20,350

2025-09-25 14:59:43:262:----脚本运行成功，请不要关闭窗口----

2025-09-25 14:59:43:262:----开始运行脚本----
共检测到1个cookie
下次抢券时间：15:00:00
当前时间段没有优惠券需要领取！

2025-09-25 15:00:00:32:未到任务执行时间，跳过执行......
加载API:4-4
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：3,3,1,100,20,350

2025-09-25 17:59:42:363:----脚本运行成功，请不要关闭窗口----

2025-09-25 17:59:42:363:----开始运行脚本----
共检测到1个cookie
下次抢券时间：18:00:00
当前时间段没有优惠券需要领取！

2025-09-25 18:00:00:26:未到任务执行时间，跳过执行......
未配置自定义API！
读取环境变量成功：1,3,5,100,20,240

2025-09-25 19:32:36:305:----脚本运行成功，请不要关闭窗口----

2025-09-25 19:32:36:305:----开始运行脚本----
共检测到5个cookie
下次抢券时间：20:00:00
名称：鸡蛋
27分后才开始！

2025-09-25 19:35:00:22:未到任务执行时间，跳过执行......

2025-09-25 19:40:00:18:未到任务执行时间，跳过执行......

2025-09-25 19:45:00:17:未到任务执行时间，跳过执行......

2025-09-25 19:50:00:30:未到任务执行时间，跳过执行......

2025-09-25 19:55:00:28:未到任务执行时间，跳过执行......
未配置自定义API！
读取环境变量成功：1,3,5,100,20,240

2025-09-25 19:55:46:588:----脚本运行成功，请不要关闭窗口----

2025-09-25 19:55:46:588:----开始运行脚本----
共检测到5个cookie
下次抢券时间：20:00:00
名称：鸡蛋
4分后才开始！

2025-09-25 19:59:00:26:----开始运行脚本----
共检测到5个cookie
下次抢券时间：20:00:00
名称：鸡蛋
0分后开始任务，请不要结束任务！


***开始领券【鸡蛋】第1次请求***


1、2025-09-25 19:59:59:767:开始领取鸡蛋_账号1


2、2025-09-25 19:59:59:807:开始领取鸡蛋_账号2


3、2025-09-25 19:59:59:820:开始领取鸡蛋_账号3


4、2025-09-25 19:59:59:827:开始领取鸡蛋_账号4


5、2025-09-25 19:59:59:954:开始领取鸡蛋_账号5


*鸡蛋_【账号3】jd_47cccc568ace6*
2025-09-25 20:00:00:183:{"code":"0","success":true,"data":{"batchId":"1350522839","rushBuyFlag":true}}


*鸡蛋_【账号1】1907992-14820856*
2025-09-25 20:00:00:184:{"code":"0","success":true,"data":{"batchId":"1350522839","rushBuyFlag":true}}


*鸡蛋_【账号4】JRSD_YFlLe0305*
2025-09-25 20:00:00:199:{"code":"0","success":true,"data":{"batchId":"1350522839","rushBuyFlag":true}}


*鸡蛋_【账号5】armishi*
2025-09-25 20:00:00:206:{"code":"0","success":true,"data":{"batchId":"1350522839","rushBuyFlag":true}}


*鸡蛋_【账号2】jd_62a1f82c3247f*
2025-09-25 20:00:00:231:{"code":"0","success":true,"data":{"batchId":"1350522839","rushBuyFlag":true}}

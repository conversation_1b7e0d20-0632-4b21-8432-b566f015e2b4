import requests
import time
from typing import Optional, Dict, List

def get_latest_non_top_from_top5(uid: str) -> Optional[Dict]:
    """
    请求微博接口获取前5条数据，过滤置顶后取最新1条非置顶微博
    :param uid: 目标用户ID（5371906414）
    :return: 最新非置顶微博数据，无则返回None
    """
    url = "https://weibo.com/ajax/statuses/mymblog"
    params = {
        "uid": uid,
        "page": 1,
        "feature": 0,
        "limit": 5  # 关键：请求前5条数据，确保覆盖可能的置顶
    }
    
    # 替换为自己的微博登录Cookie（获取方式见之前说明）
    headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36",
    "Cookie": "SCF=AsUJ4IJt7BZBVKqT8D6w2sn6hI58_2DKRC2XWSYuYPBbIbJh51C212wy5sil7i0a7xaM9Ges7f4omItuMCEEumM.; SINAGLOBAL=3092056003011.4917.1737364748051; SUBP=0033WrSXqPxfM725Ws9jqgMF55529P9D9WFoAWpxHiqBpWNERgywp33X5JpX5KMhUgL.Foz4ShMESh2ESK-2dJLoIEXLxK-LBo5L12qLxK.L1KeLB-BLxKqLBo5LBo-LxK-L1K5L12eLxK-LB-BLBKqt; UOR=,,www.baidu.com; XSRF-TOKEN=5Q_Rcv96Ttq_m4-ewDnPexHM; ALF=1760324970; SUB=_2A25FwJA6DeRhGeRH71UT9C_OzjmIHXVmv63yrDV8PUJbkNAbLXD8kW1NTbvGNJ0Dp1URQm4gR8AKh3ORTW_4PqBh; _s_tentry=weibo.com; Apache=5733488345874.564.1757732977977; ULV=1757732977979:36:3:2:5733488345874.564.1757732977977:1757402044438; WBPSESS=1OTZ7xbWCSeCMVZgKmhrIBJq0oGN_F3wbgTnYbLqK3Qkjdd-CKCYUfslG96zL-Jho_q8GsVhAJkAQE-wOnkbi6KVISmru4CF3zHipKvg2rTEmirTWUtS3UYgTMSukTQQpZj7mE24LIu-eM6W2rxe8A==",
    "Referer": "https://weibo.com",  # 模拟浏览器Referer，避免被拦截
    }
    
    try:
        response = requests.get(url=url, params=params, headers=headers, timeout=5)
        response.raise_for_status()  # 非200状态码（如403/500）抛异常
        data = response.json()
        
        # 先校验接口是否成功返回
        if data.get("ok") != 1:
            print(f"接口异常：{data.get('msg', '未知错误')}")
            return None
        
        # 提取前5条微博列表
        top5_mblogs = data.get("data", {}).get("list", [])
        if not top5_mblogs:
            print("前5条数据为空，未获取到微博")
            return None
        
        # 过滤置顶微博：排除 isTop=1 的数据
        non_top_mblogs = [
            mblog for mblog in top5_mblogs 
            if mblog.get("isTop") != 1  # 置顶微博含 isTop:1，非置顶无该字段或为0
        ]
        
        if not non_top_mblogs:
            print("前5条数据均为置顶微博，无有效非置顶内容")
            return None
        
        # 取过滤后最新的1条（前5条已按时间降序排列，第1条即为最新）
        latest_non_top = non_top_mblogs[0]
        return latest_non_top
    
    except requests.exceptions.RequestException as e:
        print(f"请求失败：{str(e)}")
        return None

def print_latest_weibo(latest_mblog: Optional[Dict]) -> None:
    """打印最新非置顶微博的关键信息（含text_raw）"""
    print("=" * 60)
    if not latest_mblog:
        print("无有效非置顶微博可打印\n")
        print("=" * 60 + "\n")
        return
    
    # 提取核心字段：发布时间、微博ID、text_raw（便于区分数据唯一性和时效性）
    created_at = latest_mblog.get("created_at", "未知时间")
    weibo_id = latest_mblog.get("idstr", "未知ID")
    text_raw = latest_mblog.get("text_raw", "无文本内容")
    
    print(f"【前5条过滤置顶后-最新微博】")
    print(f"发布时间：{created_at} | 微博ID：{weibo_id}")
    print(f"内容：\n{text_raw}\n")
    print("=" * 60 + "\n")

if __name__ == "__main__":
    TARGET_UID = "5371906414"  # 固定目标用户
    INTERVAL = 0.5  # 半秒执行一次
    print(f"开始定时任务：请求前5条数据→过滤置顶→打印最新非置顶微博")
    print(f"执行频率：每{INTERVAL}秒一次 | 按 Ctrl+C 停止\n")
    
    try:
        while True:
            # 1. 获取前5条→过滤置顶→取最新1条
            latest_weibo = get_latest_non_top_from_top5(uid=TARGET_UID)
            # 2. 打印结果
            print_latest_weibo(latest_weibo)
            # 3. 定时等待
            time.sleep(INTERVAL)
    except KeyboardInterrupt:
        print("\n已手动停止定时任务")
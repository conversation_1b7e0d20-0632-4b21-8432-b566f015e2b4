加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：2,3,3,20,20,330

2025-09-23 09:59:42:150:----脚本运行成功，请不要关闭窗口----

2025-09-23 09:59:42:151:----开始运行脚本----
共检测到2个cookie
下次抢券时间：10:00:00
名称：话费
0分后开始任务，请不要结束任务！


***开始领券【话费】第1次请求***


1、2025-09-23 09:59:59:688:开始领取话费_账号1


2、2025-09-23 09:59:59:724:开始领取话费_账号2


***开始领券【话费】第2次请求***


3、2025-09-23 09:59:59:734:开始领取话费_账号1


4、2025-09-23 09:59:59:854:开始领取话费_账号2


*话费_【账号1】1907992-14820856*
2025-09-23 10:00:00:23:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号1】1907992-14820856*
2025-09-23 10:00:00:24:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号2】zhq115*
2025-09-23 10:00:00:305:本时段优惠券已抢完，请15:00再来吧！


*话费_【账号2】zhq115*
2025-09-23 10:00:00:341:本时段优惠券已抢完，请15:00再来吧！
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：2,3,3,20,20,330

2025-09-23 14:59:43:376:----脚本运行成功，请不要关闭窗口----

2025-09-23 14:59:43:377:----开始运行脚本----
共检测到2个cookie
下次抢券时间：15:00:00
名称：话费
0分后开始任务，请不要结束任务！


***开始领券【话费】第1次请求***


1、2025-09-23 14:59:59:673:开始领取话费_账号1


2、2025-09-23 14:59:59:707:开始领取话费_账号2


***开始领券【话费】第2次请求***


3、2025-09-23 14:59:59:717:开始领取话费_账号1


4、2025-09-23 14:59:59:841:开始领取话费_账号2


*话费_【账号1】1907992-14820856*
2025-09-23 14:59:59:979:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号1】1907992-14820856*
2025-09-23 14:59:59:981:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号2】zhq115*
2025-09-23 14:59:59:997:时间未到继续：本时段优惠券已抢完，请15:00再来吧！


*话费_【账号2】zhq115*
2025-09-23 15:00:00:16:本时段优惠券已抢完，请18:00再来吧！
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：2,3,3,20,20,330

2025-09-23 17:59:42:243:----脚本运行成功，请不要关闭窗口----

2025-09-23 17:59:42:243:----开始运行脚本----
共检测到2个cookie
下次抢券时间：18:00:00
名称：话费
0分后开始任务，请不要结束任务！


***开始领券【话费】第1次请求***


1、2025-09-23 17:59:59:681:开始领取话费_账号1


2、2025-09-23 17:59:59:717:开始领取话费_账号2


***开始领券【话费】第2次请求***


3、2025-09-23 17:59:59:727:开始领取话费_账号1


4、2025-09-23 17:59:59:860:开始领取话费_账号2


*话费_【账号1】1907992-14820856*
2025-09-23 18:00:00:48:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号1】1907992-14820856*
2025-09-23 18:00:00:49:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号2】zhq115*
2025-09-23 18:00:00:50:本时段优惠券已抢完，请20:00再来吧！


*话费_【账号2】zhq115*
2025-09-23 18:00:00:51:本时段优惠券已抢完，请20:00再来吧！
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：2,3,3,20,20,330

2025-09-23 19:59:42:134:----脚本运行成功，请不要关闭窗口----

2025-09-23 19:59:42:135:----开始运行脚本----
共检测到2个cookie
下次抢券时间：20:00:00
名称：话费
0分后开始任务，请不要结束任务！


***开始领券【话费】第1次请求***


1、2025-09-23 19:59:59:674:开始领取话费_账号1


2、2025-09-23 19:59:59:705:开始领取话费_账号2


***开始领券【话费】第2次请求***


3、2025-09-23 19:59:59:714:开始领取话费_账号1


4、2025-09-23 19:59:59:822:开始领取话费_账号2


*话费_【账号1】1907992-14820856*
2025-09-23 19:59:59:957:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号1】1907992-14820856*
2025-09-23 19:59:59:958:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号2】zhq115*
2025-09-23 19:59:59:961:时间未到继续：本时段优惠券已抢完，请20:00再来吧！


*话费_【账号2】zhq115*
2025-09-23 20:00:00:8:时间未到继续：本时段优惠券已抢完，请20:00再来吧！
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：2,3,3,20,20,330

2025-09-23 21:59:42:62:----脚本运行成功，请不要关闭窗口----

2025-09-23 21:59:42:62:----开始运行脚本----
共检测到2个cookie
下次抢券时间：22:00:00
名称：话费
0分后开始任务，请不要结束任务！


***开始领券【话费】第1次请求***


1、2025-09-23 21:59:59:673:开始领取话费_账号1


2、2025-09-23 21:59:59:710:开始领取话费_账号2


***开始领券【话费】第2次请求***


3、2025-09-23 21:59:59:722:开始领取话费_账号1


4、2025-09-23 21:59:59:822:开始领取话费_账号2


*话费_【账号1】1907992-14820856*
2025-09-23 21:59:59:985:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号1】1907992-14820856*
2025-09-23 21:59:59:987:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号2】zhq115*
2025-09-23 21:59:59:988:时间未到继续：本时段优惠券已抢完，请22:00再来吧！


*话费_【账号2】zhq115*
2025-09-23 22:00:00:38:领取成功！感谢您的参与，祝您购物愉快~,subCode2_|A1|

************************


券【话费】成功领取的用户有：
2、zhq115
读取环境变量立即执行优惠券为：话费
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：1,3,1,20,20,330

2025-09-23 23:45:43:940:----脚本运行成功，请不要关闭窗口----

2025-09-23 23:45:43:941:----开始运行脚本----
共检测到3个cookie
下次抢券时间：0:00:00
立即抢券（跑完记得删除或禁用该环境变量）：话费


1、2025-09-23 23:45:43:944:开始领取话费_账号1
14分后才开始！


2、2025-09-23 23:45:44:103:开始领取话费_账号2


3、2025-09-23 23:45:44:121:开始领取话费_账号3


*话费_【账号3】jd_vSVZIXuMHVxW*
2025-09-23 23:45:44:286:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号2】jd_614aa177acd06*
2025-09-23 23:45:44:287:继续：此券今日已经被抢完，请您明日再来~


*话费_【账号1】1907992-14820856*
2025-09-23 23:45:44:289:继续：此券今日已经被抢完，请您明日再来~

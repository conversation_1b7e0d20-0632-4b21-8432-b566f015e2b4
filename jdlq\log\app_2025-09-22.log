未配置自定义API！
读取环境变量成功：10000,3,1,20,20,5000

2025-09-22 13:51:09:493:----脚本运行成功，请不要关闭窗口----

2025-09-22 13:51:09:494:----开始运行脚本----
共检测到3个cookie
下次抢券时间：14:00:00
名称：医疗膨胀20号网页不行
8分后才开始！
未配置自定义API！
读取环境变量成功：10000,3,1,500,20,5000

2025-09-22 13:54:46:921:----脚本运行成功，请不要关闭窗口----

2025-09-22 13:54:46:922:----开始运行脚本----
共检测到3个cookie
下次抢券时间：14:00:00
名称：医疗膨胀20号网页不行
5分后才开始！

2025-09-22 13:55:00:30:未到任务执行时间，跳过执行......

2025-09-22 13:59:00:26:----开始运行脚本----
共检测到3个cookie
下次抢券时间：14:00:00
名称：医疗膨胀20号网页不行
0分后开始任务，请不要结束任务！


***开始领券【医疗膨胀20号网页不行】第1次请求***


1、2025-09-22 13:59:55:513:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 13:59:55:915:{"msg":"当前时间段优惠券不可领取!","code":2017,"success":false,"requestId":"4734027.70686.17585207959177036","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


2、2025-09-22 13:59:56:65:开始领取医疗膨胀20号网页不行_账号2


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 13:59:56:229:{"msg":"当前时间段优惠券不可领取!","code":2017,"success":false,"requestId":"3831415.70686.17585207962361165","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


3、2025-09-22 13:59:56:562:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第2次请求***


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 13:59:56:732:{"msg":"当前时间段优惠券不可领取!","code":2017,"success":false,"requestId":"4812309.70686.17585207967399793","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


4、2025-09-22 13:59:57:58:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 13:59:57:252:{"msg":"当前时间段优惠券不可领取!","code":2017,"success":false,"requestId":"6300320.70686.17585207972438968","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


5、2025-09-22 13:59:57:553:开始领取医疗膨胀20号网页不行_账号2


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 13:59:57:727:{"msg":"当前时间段优惠券不可领取!","code":2017,"success":false,"requestId":"4727012.70686.17585207977283042","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


6、2025-09-22 13:59:58:50:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第3次请求***


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 13:59:58:243:{"msg":"当前时间段优惠券不可领取!","code":2017,"success":false,"requestId":"6300320.70686.17585207982378977","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


7、2025-09-22 13:59:58:545:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 13:59:58:726:{"msg":"当前时间段优惠券不可领取!","code":2017,"success":false,"requestId":"6609943.70686.17585207987213836","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


8、2025-09-22 13:59:59:41:开始领取医疗膨胀20号网页不行_账号2


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 13:59:59:209:{"msg":"当前时间段优惠券不可领取!","code":2017,"success":false,"requestId":"4720669.70686.17585207992173939","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


9、2025-09-22 13:59:59:537:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第4次请求***


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 13:59:59:759:{"msg":"当前时间段优惠券不可领取!","code":2017,"success":false,"requestId":"4722600.70686.17585207997429430","class":"com.jd.hc.core.user.color.dto.ResultDTO"}

2025-09-22 14:00:00:17:未到任务执行时间，跳过执行......


10、2025-09-22 14:00:00:32:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 14:00:00:394:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"4722606.70686.17585208002267769","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


11、2025-09-22 14:00:00:528:开始领取医疗膨胀20号网页不行_账号2


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 14:00:00:880:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"5579068.70686.17585208007192599","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


12、2025-09-22 14:00:01:24:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第5次请求***


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 14:00:01:372:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6554664.70686.17585208012068543","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


13、2025-09-22 14:00:01:520:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 14:00:01:849:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"4727012.70686.17585208016993582","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


14、2025-09-22 14:00:02:16:开始领取医疗膨胀20号网页不行_账号2


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 14:00:02:344:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6599976.70686.17585208021944602","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


15、2025-09-22 14:00:02:512:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第6次请求***


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 14:00:02:855:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6554665.70686.17585208026946542","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


16、2025-09-22 14:00:03:8:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 14:00:03:347:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6610112.70686.17585208031837668","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


17、2025-09-22 14:00:03:504:开始领取医疗膨胀20号网页不行_账号2


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 14:00:03:852:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"4704973.70686.17585208036840828","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


18、2025-09-22 14:00:04:0:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第7次请求***


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 14:00:04:346:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6610112.70686.17585208041857793","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


19、2025-09-22 14:00:04:496:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 14:00:04:841:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6300320.70686.17585208046819870","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


20、2025-09-22 14:00:04:991:开始领取医疗膨胀20号网页不行_账号2


21、2025-09-22 14:00:05:488:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第8次请求***


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 14:00:05:867:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"5579071.70686.17585208057000351","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


22、2025-09-22 14:00:05:984:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 14:00:06:358:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6609941.70686.17585208061719441","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 14:00:06:363:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6554664.70686.17585208061679176","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


23、2025-09-22 14:00:06:480:开始领取医疗膨胀20号网页不行_账号2


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 14:00:06:817:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6610112.70686.17585208066588164","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


24、2025-09-22 14:00:06:976:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第9次请求***


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 14:00:07:326:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6609943.70686.17585208071585087","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


25、2025-09-22 14:00:07:471:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 14:00:07:785:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"4720669.70686.17585208076425328","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


26、2025-09-22 14:00:07:967:开始领取医疗膨胀20号网页不行_账号2


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 14:00:08:280:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"4734027.70686.17585208081328497","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


27、2025-09-22 14:00:08:462:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第10次请求***


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 14:00:08:798:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6609943.70686.17585208086385314","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


28、2025-09-22 14:00:08:959:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 14:00:09:233:{"msg":"成功","code":0,"data":[{"couponName":"超市补贴神券","activityKey":"BA_6hxujz4","maxClaimLimit":0,"discount":"30","receiveStatus":5,"couponId":"239171067496","batchId":"1285989474","couponStyle":0,"maxDiscount":"","couponType":1,"quota":"299","strategyId":320,"beginTime":1758470400000,"endTime":1758643199000,"class":"com.jd.hc.core.user.color.dto.CouponExpansionBaseDto","status":2}],"success":true,"requestId":"6610110.70686.17585208091550611","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


29、2025-09-22 14:00:09:454:开始领取医疗膨胀20号网页不行_账号2


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 14:00:09:769:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6600117.70686.17585208096242737","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


30、2025-09-22 14:00:09:950:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第11次请求***


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 14:00:10:305:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6609942.70686.17585208101437751","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


31、2025-09-22 14:00:10:446:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 14:00:10:608:{"msg":"您已领取该优惠券","code":2002,"success":false,"requestId":"3831415.70686.17585208106303066","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


32、2025-09-22 14:00:10:942:开始领取医疗膨胀20号网页不行_账号2


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 14:00:11:281:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"4704973.70686.17585208111201916","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


33、2025-09-22 14:00:11:438:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第12次请求***


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 14:00:11:778:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"4704973.70686.17585208116171988","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


34、2025-09-22 14:00:11:934:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 14:00:12:80:{"msg":"您已领取该优惠券","code":2002,"success":false,"requestId":"6554672.70686.17585208121018300","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


35、2025-09-22 14:00:12:430:开始领取医疗膨胀20号网页不行_账号2


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 14:00:12:781:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6554665.70686.17585208126138499","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


36、2025-09-22 14:00:12:926:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第13次请求***


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 14:00:13:286:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"4722606.70686.17585208131079612","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


37、2025-09-22 14:00:13:422:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 14:00:13:615:{"msg":"您已领取该优惠券","code":2002,"success":false,"requestId":"4717869.70686.17585208136309217","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


38、2025-09-22 14:00:13:919:开始领取医疗膨胀20号网页不行_账号2


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 14:00:14:243:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6599977.70686.17585208140984730","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


39、2025-09-22 14:00:14:414:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第14次请求***


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 14:00:14:731:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6599977.70686.17585208145904800","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


40、2025-09-22 14:00:14:910:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 14:00:15:55:{"msg":"您已领取该优惠券","code":2002,"success":false,"requestId":"6600117.70686.17585208150773668","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


41、2025-09-22 14:00:15:406:开始领取医疗膨胀20号网页不行_账号2


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 14:00:15:754:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"4722600.70686.17585208155952212","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


42、2025-09-22 14:00:15:902:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第15次请求***


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 14:00:16:249:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"4722600.70686.17585208160862299","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


43、2025-09-22 14:00:16:397:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 14:00:16:561:{"msg":"您已领取该优惠券","code":2002,"success":false,"requestId":"6609940.70686.17585208165770816","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


44、2025-09-22 14:00:16:894:开始领取医疗膨胀20号网页不行_账号2


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 14:00:17:242:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"4717869.70686.17585208170749845","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


45、2025-09-22 14:00:17:390:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第16次请求***


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 14:00:17:754:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6599977.70686.17585208176125346","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


46、2025-09-22 14:00:17:886:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 14:00:18:61:{"msg":"您已领取该优惠券","code":2002,"success":false,"requestId":"4704973.70686.17585208180703142","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


47、2025-09-22 14:00:18:382:开始领取医疗膨胀20号网页不行_账号2


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 14:00:18:788:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"4722600.70686.17585208186242781","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


48、2025-09-22 14:00:18:877:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第17次请求***


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 14:00:19:188:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6600118.70686.17585208190493069","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


49、2025-09-22 14:00:19:374:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 14:00:19:520:{"msg":"您已领取该优惠券","code":2002,"success":false,"requestId":"6600117.70686.17585208195444516","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


50、2025-09-22 14:00:19:870:开始领取医疗膨胀20号网页不行_账号2


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 14:00:20:186:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"4734027.70686.17585208200410572","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


51、2025-09-22 14:00:20:366:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第18次请求***


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 14:00:20:790:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6300320.70686.17585208205632547","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


52、2025-09-22 14:00:20:863:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 14:00:21:41:{"msg":"您已领取该优惠券","code":2002,"success":false,"requestId":"6554666.70686.17585208210551369","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


53、2025-09-22 14:00:21:358:开始领取医疗膨胀20号网页不行_账号2


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 14:00:21:662:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6600117.70686.17585208215224843","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


54、2025-09-22 14:00:21:853:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第19次请求***


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 14:00:22:191:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6554664.70686.17585208220321916","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


55、2025-09-22 14:00:22:349:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 14:00:22:516:{"msg":"您已领取该优惠券","code":2002,"success":false,"requestId":"6554664.70686.17585208225321999","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


56、2025-09-22 14:00:22:845:开始领取医疗膨胀20号网页不行_账号2


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 14:00:23:190:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6300320.70686.17585208230262973","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


57、2025-09-22 14:00:23:341:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第20次请求***


58、2025-09-22 14:00:23:837:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 14:00:23:940:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6609941.70686.17585208237662424","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 14:00:24:2:{"msg":"您已领取该优惠券","code":2002,"success":false,"requestId":"4727012.70686.17585208240257187","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


59、2025-09-22 14:00:24:333:开始领取医疗膨胀20号网页不行_账号2


60、2025-09-22 14:00:24:829:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第21次请求***


*医疗膨胀20号网页不行_【账号3】jd_51b29ca7e9a2b*
2025-09-22 14:00:25:159:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6599977.70686.17585208250176738","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


*医疗膨胀20号网页不行_【账号2】jd_vSVZIXuMHVxW*
2025-09-22 14:00:25:162:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"6554669.70686.17585208250184027","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


61、2025-09-22 14:00:25:325:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】jd_614aa177acd06*
2025-09-22 14:00:25:492:{"msg":"您已领取该优惠券","code":2002,"success":false,"requestId":"3831415.70686.17585208255115764","class":"com.jd.hc.core.user.color.dto.ResultDTO"}


62、2025-09-22 14:00:25:821:开始领取医疗膨胀20号网页不行_账号2
"Response code 403 (Forbidden)"
API请求失败，请检查网络重试


63、2025-09-22 14:00:26:316:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第22次请求***
"Response code 403 (Forbidden)"
API请求失败，请检查网络重试


64、2025-09-22 14:00:26:812:开始领取医疗膨胀20号网页不行_账号1
"Response code 403 (Forbidden)"
API请求失败，请检查网络重试


65、2025-09-22 14:00:27:308:开始领取医疗膨胀20号网页不行_账号2
"Response code 403 (Forbidden)"
API请求失败，请检查网络重试


66、2025-09-22 14:00:27:803:开始领取医疗膨胀20号网页不行_账号3


***开始领券【医疗膨胀20号网页不行】第23次请求***
"Response code 403 (Forbidden)"
API请求失败，请检查网络重试


67、2025-09-22 14:00:28:299:开始领取医疗膨胀20号网页不行_账号1
"Response code 403 (Forbidden)"
API请求失败，请检查网络重试


68、2025-09-22 14:00:28:795:开始领取医疗膨胀20号网页不行_账号2
"Response code 403 (Forbidden)"
API请求失败，请检查网络重试
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：2,3,3,20,20,350

2025-09-22 14:59:46:494:----脚本运行成功，请不要关闭窗口----

2025-09-22 14:59:46:494:----开始运行脚本----
共检测到3个cookie
下次抢券时间：15:00:00
名称：话费
0分后开始任务，请不要结束任务！


***开始领券【话费】第1次请求***


1、2025-09-22 14:59:59:677:开始领取话费_账号1


2、2025-09-22 14:59:59:764:开始领取话费_账号2


3、2025-09-22 14:59:59:777:开始领取话费_账号3


***开始领券【话费】第2次请求***


4、2025-09-22 15:00:00:15:开始领取话费_账号1


5、2025-09-22 15:00:00:33:开始领取话费_账号2


6、2025-09-22 15:00:00:51:开始领取话费_账号3


*话费_【账号1】jd_vSVZIXuMHVxW*
2025-09-22 15:00:00:351:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号1】jd_vSVZIXuMHVxW*
2025-09-22 15:00:00:352:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号2】1907992-14820856*
2025-09-22 15:00:00:353:本时段优惠券已抢完，请18:00再来吧！


*话费_【账号2】1907992-14820856*
2025-09-22 15:00:00:353:本时段优惠券已抢完，请18:00再来吧！


*话费_【账号3】jd_414c6286ab55d*
2025-09-22 15:00:00:354:本时段优惠券已抢完，请18:00再来吧！


*话费_【账号3】jd_414c6286ab55d*
2025-09-22 15:00:00:354:本时段优惠券已抢完，请18:00再来吧！
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：2,3,3,20,20,350

2025-09-22 17:59:42:19:----脚本运行成功，请不要关闭窗口----

2025-09-22 17:59:42:19:----开始运行脚本----
共检测到2个cookie
下次抢券时间：18:00:00
名称：话费
0分后开始任务，请不要结束任务！


***开始领券【话费】第1次请求***


1、2025-09-22 17:59:59:657:开始领取话费_账号1


2、2025-09-22 17:59:59:694:开始领取话费_账号2


***开始领券【话费】第2次请求***


3、2025-09-22 17:59:59:701:开始领取话费_账号1


4、2025-09-22 17:59:59:803:开始领取话费_账号2


*话费_【账号1】1907992-14820856*
2025-09-22 17:59:59:947:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号1】1907992-14820856*
2025-09-22 17:59:59:949:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号2】jd_414c6286ab55d*
2025-09-22 17:59:59:953:时间未到继续：本时段优惠券已抢完，请18:00再来吧！


*话费_【账号2】jd_414c6286ab55d*
2025-09-22 17:59:59:975:时间未到继续：本时段优惠券已抢完，请18:00再来吧！
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：2,3,3,20,20,340

2025-09-22 19:59:42:61:----脚本运行成功，请不要关闭窗口----

2025-09-22 19:59:42:62:----开始运行脚本----
共检测到2个cookie
下次抢券时间：20:00:00
名称：话费
0分后开始任务，请不要结束任务！


***开始领券【话费】第1次请求***


1、2025-09-22 19:59:59:675:开始领取话费_账号1


2、2025-09-22 19:59:59:710:开始领取话费_账号2


***开始领券【话费】第2次请求***


3、2025-09-22 19:59:59:722:开始领取话费_账号1


4、2025-09-22 19:59:59:828:开始领取话费_账号2


*话费_【账号2】jd_414c6286ab55d*
2025-09-22 19:59:59:974:时间未到继续：本时段优惠券已抢完，请20:00再来吧！


*话费_【账号1】1907992-14820856*
2025-09-22 19:59:59:975:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号1】1907992-14820856*
2025-09-22 19:59:59:976:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号2】jd_414c6286ab55d*
2025-09-22 20:00:00:50:本时段优惠券已抢完，请22:00再来吧！
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：2,3,3,20,20,330

2025-09-22 21:59:42:3:----脚本运行成功，请不要关闭窗口----

2025-09-22 21:59:42:4:----开始运行脚本----
共检测到2个cookie
下次抢券时间：22:00:00
名称：话费
0分后开始任务，请不要结束任务！


***开始领券【话费】第1次请求***


1、2025-09-22 21:59:59:675:开始领取话费_账号1


2、2025-09-22 21:59:59:706:开始领取话费_账号2


***开始领券【话费】第2次请求***


3、2025-09-22 21:59:59:717:开始领取话费_账号1


4、2025-09-22 21:59:59:821:开始领取话费_账号2


*话费_【账号1】1907992-14820856*
2025-09-22 21:59:59:959:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号1】1907992-14820856*
2025-09-22 21:59:59:960:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号2】jd_414c6286ab55d*
2025-09-22 21:59:59:974:时间未到继续：本时段优惠券已抢完，请22:00再来吧！


*话费_【账号2】jd_414c6286ab55d*
2025-09-22 22:00:00:31:领取成功！感谢您的参与，祝您购物愉快~,subCode2_|A1|

************************


券【话费】成功领取的用户有：
2、jd_414c6286ab55d

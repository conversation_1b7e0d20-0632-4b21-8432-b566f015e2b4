#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
联通→迅雷超级会员月卡抢购（支持线程池、次数配置及账号追踪）
逻辑：使用线程池并发抢购，日志中包含账号信息，方便查看每个账号的请求情况
"""

import os, re, json, time, ssl, certifi
import httpx
from dataclasses import dataclass
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from log import logger
import logging
# 将 httpx 相关日志级别设为 WARNING 及以上，避免打印 HTTP 请求等详细日志
logging.getLogger("httpx").setLevel(logging.WARNING)


# -------------------- 工具函数 --------------------
ssl_context = ssl.create_default_context(cafile=certifi.where())
ssl_context.set_ciphers('DEFAULT@SECLEVEL=1')

def log(msg, level="info", phone=None):
    colors = {"info": "\033[0m", "ok": "\033[92m", "warn": "\033[93m", "err": "\033[91m"}
    prefix = {"info": "ℹ️", "ok": "✅", "warn": "⚠️", "err": "❌"}.get(level, "ℹ️")
    # 增加账号信息显示，隐藏中间4位保护隐私
    phone_str = f"[{phone[:3]}****{phone[-4:]}] " if phone else ""
    logger.info(f"{colors[level]}[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] {prefix} {phone_str}{msg}\033[0m")

def env(key, default=""):
    return os.environ.get(key, default).strip()

def split_tokens():
    # ⚠️ 改成你自己的 token
    raw = "037cc84a13842bb219e5e77ae5991b656c8a2b3c8fdee277acac9ee39db590f548adaaff719f25ab71ae87d986038320c1220be764441a6d52327923d94c1a09739c67bc8dc4ec46f5d28c9c9135c66caa29b8d19c2103b9cd2dfb96bba6e149a8cf0cc0fadd78fb1b98a5912e3a7024e8ea970a4febbc3420e7113c1817337dbadca592c34499bef00a73e7da898a3e19c3fd4b6294c4b91b6c371eb1a9e1ac28818083ac7dfa61eba6159c8701d1df18658790d05575f04a51b8f275952349f56d7784691669444527b11d9509626ee204ca6f78988527abdcf4f599dc5dff6f0f4a1eb3c4e9ecc08db0821825d80093561d868abc37c935db5457270817015b636eb96438db723ed74e0dd491a3a66e96d6af4d506a3d0ee017907783e3e4e7f7a58719d0c47b29b9483828858120"
    return re.split(r'[\n&^@%]+', raw.strip()) if raw else []

# -------------------- 配置区 --------------------
@dataclass
class Config:
    UA: str = env("MOBILE_UA") or (
        "Mozilla/5.0 (Linux; Android 11; Mi 10) "
        "AppleWebKit/537.36 Chrome/87.0.4280.141 Mobile Safari/537.36"
    )
    SLEEP: float = float(env("RUSH_SLEEP") or "0.00001")  # 每次请求间隔时间(秒)
    START_TIME: str = env("START_TIME") or "12:59:57.000"  # 支持毫秒，例如 09:59:59.500
    MAX_RUSH_TIMES: int = int(env("MAX_RUSH_TIMES") or "5")  # 每个账号单次抢购的最大尝试次数
    MAX_WORKERS: int = int(env("MAX_WORKERS") or "10")  # 线程池最大线程数
    ACCOUNT_RUN_TIMES: int = 5  # 每个账号运行几次抢购流程
    PRODUCT_CODE: str = "**********"
    ACTIVITY_CODE: str = "YOUCHOICEONE"
    ACTIVITY_ID: int = 11
    PRODUCT_ID: int = 271

CFG = Config()

# -------------------- 业务类 --------------------
class RushVip:
    def __init__(self, token, run_id=0):
        self.token  = token.strip()
        self.phone  = None  # 保存手机号
        self.bearer = None
        self.client = httpx.Client(verify=ssl_context, timeout=30)
        self.success = False  # 标记是否成功抢购
        self.run_id = run_id  # 用于标识同一账号的不同轮次

    def close(self):
        self.client.close()

    def login(self):
        try:
            r1 = self.client.post(
                "https://m.client.10010.com/mobileService/onLine.htm",
                data={"isFirstInstall": "1", "version": "android@11.0702", "token_online": self.token}
            )
            ecs = r1.json().get("ecs_token")
            if not ecs:
                log(f"第{self.run_id+1}轮：ecs_token 获取失败", "err"); return False

            r2 = self.client.get(
                "https://m.client.10010.com/mobileService/openPlatform/openPlatLineNew.htm",
                params={"to_url": "https://contact.bol.wo.cn/market"},
                headers={"Cookie": f"ecs_token={ecs}"}, follow_redirects=False
            )
            ticket = r2.headers.get("Location", "").split("ticket=")[-1].split("&")[0]
            if not ticket:
                log(f"第{self.run_id+1}轮：ticket 获取失败", "err"); return False

            r3 = self.client.post(
                f"https://backward.bol.wo.cn/prod-api/auth/marketUnicomLogin?ticket={ticket}"
            )
            self.bearer = r3.json().get("data", {}).get("token")
            self.phone  = r1.json().get("desmobile")

            if self.bearer:
                log(f"第{self.run_id+1}轮：登录成功", "ok", self.phone)
                return True
            return False
        except Exception as e:
            log(f"第{self.run_id+1}轮：登录异常: {e}", "err", self.phone)
            return False

    def order(self):
        if self.success:  # 如果已经成功，不再继续请求
            return 1
            
        url = ("https://backward.bol.wo.cn/prod-api/promotion/activity/roll/receiveRights?yGdtco4r=0OHXjiGEqWtdwrYv4iwSbrTXqayPd0cVd22KP.hsplVnsy2NGqp5M2NzpIjOJP9t_zwlH.u7mpEmAOBiG4LSbEYJPgS0b5dZEoYTNIfw1gK4DmdKeljHAtyJOeJ5zo_bwc_hfJtADHzhagZUBxKpYoubO_Chheqf0z6TN__9Ynl5YbxQlezYyA58aRBwfFRCYGr_CGnWdpejfyKplHHGDPeR5gQ4qkRBC78N7gg7_ZuegrxGZKB7BeK1by4aDmAYbZZIDBA1InZWqtq8VDT90JTjRuePoRvAxa1dfhUAw6m0dorxrnFMqC0ZvHIVzS7UyDeBn7QZvTdUqY9aTtYjP8DAQHSmw7CH6LnKaftizEE6yMcXK0tRiSohSmIRlzWfn.VMhdFCp.Llbq4IXJdzVWRh1jgcVj3AvF4u3u0O7rUhax8kCSRkdVT5uujKakCGyxwIKslJuzyxlZl8Uo2x4.Ig8Iu.DCyybEQxDuu6dpkw1HIIt1rVpr5IYn4zx1AtvFuG3QY8Jjzr1")
        headers = {
            "Authorization": f"Bearer {self.bearer}",
            "Content-Type": "application/json",
            "User-Agent": CFG.UA,
            "Origin": "https://contact.bol.wo.cn",
            "Referer": "https://contact.bol.wo.cn",
        }
        now = datetime.now()
        body = {
            "channelId": None,
            "activityId": CFG.ACTIVITY_ID,
            "productId": CFG.PRODUCT_ID,
            "productCode": CFG.PRODUCT_CODE,
            "currentTime": f"{now.year}-{now.month}-{now.day}",
            "accountType": "1",
            'account':'*********'
        }
        try:
            r = self.client.post(url, headers=headers, json=body)
            data = r.json()
            code = data.get("code")
            msg  = data.get("msg", "")
            log(f"第{self.run_id+1}轮：领取返回 code={code}, msg={msg}", "info", self.phone)

            if (code == 200 and any(k in msg for k in ["成功", "已兑换", "已领取"])) or \
               (code == 500 and "已参与" in msg):
                log(f"第{self.run_id+1}轮：抢购成功！（已抢到）", "ok", self.phone)
                self.success = True
                return 1
            if "已抢空" in msg or "不足" in msg:
                return 0
            return 0
        except Exception as e:
            log(f"第{self.run_id+1}轮：order 异常: {e}", "err", self.phone)
            return 0

    def run(self):
        if not self.login():
            return False

        # 🚀 精准等待到毫秒
        now = datetime.now()
        t = datetime.strptime(CFG.START_TIME, "%H:%M:%S.%f")
        target = now.replace(hour=t.hour, minute=t.minute, second=t.second,
                             microsecond=t.microsecond)
        if target < now:
            target += timedelta(days=1)

        wait_sec = (target - now).total_seconds()
        if wait_sec > 0:
            log(f"第{self.run_id+1}轮：等待 {wait_sec:.3f} 秒，到 {CFG.START_TIME}", "info", self.phone)
            start = time.perf_counter()
            while True:
                if (time.perf_counter() - start) >= wait_sec:
                    break

        log(f"第{self.run_id+1}轮：🚀 进入抢购时段！", "ok", self.phone)
        
        # 执行指定次数的抢购
        for i in range(CFG.MAX_RUSH_TIMES):
            if self.success:  # 如果已经成功，提前退出
                break
                
            result = self.order()
            if result == 1:
                break
                
            # 控制请求频率
            time.sleep(CFG.SLEEP)
        
        if not self.success:
            log(f"第{self.run_id+1}轮：抢购次数已用完，未成功抢购", "warn", self.phone)
            
        return self.success

# -------------------- main --------------------
def main():
    tokens = split_tokens()
    if not tokens:
        log("未配置 token", "err"); return
    log(f"共 {len(tokens)} 个账号，每个账号运行 {CFG.ACCOUNT_RUN_TIMES} 轮，"
        f"每轮尝试 {CFG.MAX_RUSH_TIMES} 次，线程池大小: {CFG.MAX_WORKERS}", "info")

    # 创建线程池
    with ThreadPoolExecutor(max_workers=CFG.MAX_WORKERS) as executor:
        # 为每个token创建多次任务（根据ACCOUNT_RUN_TIMES配置）
        futures = []
        for tk in tokens:
            for run_id in range(CFG.ACCOUNT_RUN_TIMES):
                futures.append(executor.submit(RushVip(tk, run_id).run))
        
        # 处理结果
        for future in as_completed(futures):
            try:
                success = future.result()
                if success:
                    log(f"账号抢购成功", "ok")
                else:
                    log(f"账号抢购失败", "warn")
            except Exception as e:
                log(f"账号执行出错: {e}", "err")

if __name__ == "__main__":
    main()

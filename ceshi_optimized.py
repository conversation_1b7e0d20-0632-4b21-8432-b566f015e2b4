#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东抢购脚本 - 基于现有代码的优化版本
保持原有架构，重点优化性能瓶颈
"""

from threading import Event, Lock, Semaphore
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
import requests
import json
from urllib.parse import parse_qs, urlparse
from log import logger
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from functools import lru_cache
from dataclasses import dataclass
from typing import Optional, Dict, Any
import threading

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 优化后的配置
@dataclass
class OptimizedConfig:
    # 并发配置 - 大幅提升
    MAX_THREADS: int = 16  # 从1提升到16
    MAX_H5ST_CONCURRENT: int = 5  # h5st并发限制
    
    # 网络配置 - 连接池优化
    CONNECTION_POOL_SIZE: int = 20
    CONNECTION_POOL_MAXSIZE: int = 50
    REQUEST_TIMEOUT: int = 5
    
    # 时间配置 - 提升精度
    TIMEOUT_MS: int = 3000  # 从10ms提升到3000ms
    RUSH_PREPARE_TIME: int = 3000  # 从5500ms优化到3000ms
    
    # 缓存配置
    H5ST_CACHE_TTL: int = 25  # h5st缓存25秒
    ENABLE_RETRY: bool = True
    MAX_RETRIES: int = 2

# 全局配置实例
config = OptimizedConfig()

# 目标URL和API配置
url = "https://sgm-m.jd.com/h5/"
rushTime = 1756977119000

# h5st 签名服务配置
H5ST_SERVICE_URL = "http://192.168.241.168:3001/h5st"

# 京东API配置
JD_API_BASE = "https://api.m.jd.com/client.action"
FUNCTION_ID = "newBabelAwardCollection"
APP_ID = "babelh5"

# 从原始API中提取body参数
ACTIVITY_BODY = {"activityId":"3XgsQ4Caupu9ut9814NggDCbjk4L","from":"H5node","scene":"1","args":"key=C7964E0C9BAB491A89419FB7D10D721D35AA4D23664AADA4718FDD634FDDAE255E33820A06A81C5FF77571C48BCEDFC9_bingo,roleId=A353253B2ECBCF65C26C21B29AEA635625274A4FAE1F910C5028DEDC7277097CF8FFF071906003F040A54E0FD8F4E57A88687043C304B038B16D032344B8DDC28E738EDDAFC5073101C6AEBDF413FD056BA20307F2A2E08949550A5D584AB15AEDA44520057324CB51F5972D935646979A9728255886DEA1B6ED86959A82733714E58EF4493CD0F671024FE9E30D08EC20AFD5E93A092089103724115C15796A_bingo"}

cookie = [
  "pt_key=AAJoTsn8ADAGKPUOWwvxwuMCM8KZtLUE7ccCBgTny3Gp3jta-Gv1_vHRsPOYsrlMUk836FcOpp8; pt_pin=jd_vSVZIXuMHVxW;",
]

# 优化的线程安全控制
stop_event = Event()
request_lock = Lock()
h5st_lock = Lock()
success_count = 0
account_stats = {}

# h5st缓存系统
class H5STCache:
    def __init__(self, ttl: int = 25):
        self.cache = {}
        self.ttl = ttl
        self.lock = Lock()
    
    def get(self, pt_pin: str) -> Optional[str]:
        with self.lock:
            if pt_pin in self.cache:
                timestamp, h5st = self.cache[pt_pin]
                if time.time() - timestamp < self.ttl:
                    logger.debug(f"h5st缓存命中: {pt_pin}")
                    return h5st
                else:
                    # 清理过期缓存
                    del self.cache[pt_pin]
        return None
    
    def set(self, pt_pin: str, h5st: str):
        with self.lock:
            self.cache[pt_pin] = (time.time(), h5st)
            logger.debug(f"h5st缓存更新: {pt_pin}")
    
    def clear_expired(self):
        """清理过期缓存"""
        with self.lock:
            current_time = time.time()
            expired_keys = [
                key for key, (timestamp, _) in self.cache.items()
                if current_time - timestamp >= self.ttl
            ]
            for key in expired_keys:
                del self.cache[key]

# 全局h5st缓存
h5st_cache = H5STCache(config.H5ST_CACHE_TTL)

# h5st并发控制
h5st_semaphore = Semaphore(config.MAX_H5ST_CONCURRENT)

def extract_pt_pin(cookie_str: str) -> str:
    """从cookie字符串中提取pt_pin"""
    try:
        for part in cookie_str.split(';'):
            part = part.strip()
            if part.startswith('pt_pin='):
                return part.split('=')[1]
        return "未知账号"
    except Exception:
        return "未知账号"

def create_optimized_session() -> requests.Session:
    """创建优化的requests session"""
    session = requests.Session()
    
    # 配置重试策略
    if config.ENABLE_RETRY:
        retry_strategy = Retry(
            total=config.MAX_RETRIES,
            backoff_factor=0.1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
    else:
        retry_strategy = Retry(total=0)
    
    # 配置连接池适配器
    adapter = HTTPAdapter(
        pool_connections=config.CONNECTION_POOL_SIZE,
        pool_maxsize=config.CONNECTION_POOL_MAXSIZE,
        max_retries=retry_strategy
    )
    
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    session.verify = False
    
    return session

def get_h5st_signature_optimized(pt_pin: str) -> Optional[str]:
    """优化的h5st签名获取 - 支持缓存和并发控制"""
    
    # 首先检查缓存
    cached_h5st = h5st_cache.get(pt_pin)
    if cached_h5st:
        return cached_h5st
    
    # 缓存未命中，获取新签名
    with h5st_semaphore:  # 限制并发数
        # 双重检查，避免重复请求
        cached_h5st = h5st_cache.get(pt_pin)
        if cached_h5st:
            return cached_h5st
        
        try:
            h5st_payload = {
                "version": "5.2.0",
                "pin": pt_pin,
                "ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
                "appId": APP_ID,
                "body": {
                    "functionId": FUNCTION_ID,
                    "appid": APP_ID,
                    "body": ACTIVITY_BODY
                }
            }
            
            headers = {"Content-Type": "application/json"}
            
            response = requests.post(
                H5ST_SERVICE_URL, 
                json=h5st_payload, 
                headers=headers, 
                timeout=config.REQUEST_TIMEOUT, 
                verify=False
            )
            
            if response.status_code in [200, 201]:
                result = response.json()
                if result.get("code") == 200 and "body" in result and "h5st" in result["body"]:
                    h5st_value = result["body"]["h5st"].get("h5st")
                    if h5st_value:
                        # 缓存新获取的h5st
                        h5st_cache.set(pt_pin, h5st_value)
                        logger.info(f"h5st签名获取成功: {pt_pin} - {h5st_value[:50]}...")
                        return h5st_value
                    else:
                        logger.error(f"h5st响应中缺少h5st字段: {pt_pin}")
                else:
                    logger.error(f"h5st服务返回错误: {pt_pin} - {result}")
            else:
                logger.error(f"h5st服务请求失败: {pt_pin} - {response.status_code}")
                
        except Exception as e:
            logger.error(f"获取h5st签名异常: {pt_pin} - {e}")
    
    return None

class OptimizedJDRushBuyer:
    """优化的京东抢购类"""

    def __init__(self, cookie_str: str, account_id: str):
        self.cookie_str = cookie_str
        self.pt_pin = extract_pt_pin(cookie_str)
        self.account_id = account_id
        
        # 优化的headers
        self.headers = {
            "Host": "api.m.jd.com",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Referer": "https://trade.jd.com/",
            "Connection": "keep-alive",  # 启用keep-alive
            "Cookie": cookie_str
        }
        
        # 创建优化的session
        self.session = create_optimized_session()
        self.session.headers.update(self.headers)

        # 初始化账号统计
        with request_lock:
            if self.pt_pin not in account_stats:
                account_stats[self.pt_pin] = {'success': 0, 'failed': 0, 'avg_time': 0}

    def make_request_optimized(self, thread_id: str) -> Dict[str, Any]:
        """优化的请求执行方法"""
        global success_count

        if stop_event.is_set():
            return None

        start_time = time.time() * 1000
        
        try:
            # 使用优化的h5st获取
            h5st_signature = get_h5st_signature_optimized(self.pt_pin)
            
            if not h5st_signature:
                logger.error(f"账号[{self.pt_pin}] {thread_id} - h5st签名获取失败")
                with request_lock:
                    account_stats[self.pt_pin]['failed'] += 1
                return {
                    'thread_id': thread_id,
                    'account': self.pt_pin,
                    'error': 'h5st签名获取失败',
                    'success': False
                }
            
            # 构建POST请求数据
            post_data = {
                'functionId': FUNCTION_ID,
                'appid': APP_ID,
                'body': json.dumps(ACTIVITY_BODY),
                'client': 'wh5',
                'h5st': h5st_signature,
                'clientVersion': '1.0.0'
            }
            
            # 发送POST请求
            response = self.session.post(
                JD_API_BASE, 
                data=post_data, 
                timeout=config.REQUEST_TIMEOUT
            )
            
            end_time = time.time() * 1000
            response_time = end_time - start_time

            with request_lock:
                success_count += 1
                stats = account_stats[self.pt_pin]
                stats['success'] += 1
                
                # 更新平均响应时间
                total_requests = stats['success'] + stats['failed']
                stats['avg_time'] = (stats['avg_time'] * (total_requests - 1) + response_time) / total_requests
                
                logger.info(f"账号[{self.pt_pin}] {thread_id} - 请求成功 - "
                          f"耗时: {response_time:.2f}ms - 响应: {response.text[:100]}...")

            return {
                'thread_id': thread_id,
                'account': self.pt_pin,
                'status_code': response.status_code,
                'response_time': response_time,
                'success': True
            }

        except Exception as e:
            end_time = time.time() * 1000
            response_time = end_time - start_time
            
            with request_lock:
                account_stats[self.pt_pin]['failed'] += 1
            
            logger.error(f"账号[{self.pt_pin}] {thread_id} - 请求失败: {e}")
            return {
                'thread_id': thread_id,
                'account': self.pt_pin,
                'error': str(e),
                'response_time': response_time,
                'success': False
            }

def execute_rush_requests_optimized(buyers, start_time):
    """优化的抢购执行函数"""
    global success_count

    logger.info(f"开始优化抢购执行 - 账号数: {len(buyers)}, 线程数: {config.MAX_THREADS}")
    
    # 清理过期的h5st缓存
    h5st_cache.clear_expired()
    
    # 智能分配线程
    threads_per_account = max(1, config.MAX_THREADS // len(buyers))
    actual_threads = threads_per_account * len(buyers)
    
    logger.info(f"每账号线程数: {threads_per_account}, 实际总线程数: {actual_threads}")

    # 使用优化的线程池
    with ThreadPoolExecutor(max_workers=config.MAX_THREADS) as executor:
        futures = []

        # 提交所有任务
        for buyer in buyers:
            for i in range(threads_per_account):
                if not stop_event.is_set():
                    thread_id = f"{buyer.account_id}-{i+1}"
                    future = executor.submit(buyer.make_request_optimized, thread_id)
                    futures.append(future)

        # 等待任务完成
        logger.info("等待所有请求完成...")
        completed_count = 0
        failed_count = 0

        try:
            for future in as_completed(futures, timeout=config.TIMEOUT_MS/1000):
                try:
                    result = future.result()
                    if result:
                        if result.get('success'):
                            completed_count += 1
                        else:
                            failed_count += 1
                except Exception as e:
                    failed_count += 1
                    logger.error(f"任务执行异常: {e}")
        except Exception as e:
            logger.warning(f"等待任务完成超时: {e}")

        # 输出优化的统计信息
        logger.info("=== 优化版本执行统计 ===")
        total_requests = completed_count + failed_count
        success_rate = (completed_count / max(1, total_requests)) * 100
        
        for pt_pin, stats in account_stats.items():
            logger.info(f"账号[{pt_pin}] - 成功: {stats['success']}次, "
                       f"失败: {stats['failed']}次, 平均耗时: {stats['avg_time']:.2f}ms")

        logger.info(f"抢购完成 - 成功率: {success_rate:.1f}% ({completed_count}/{total_requests})")
        logger.info(f"总成功请求数: {success_count}")

def wait_for_rush_time_optimized():
    """优化的等待函数 - 提升时间精度"""
    logger.info(f"等待抢购时间: {rushTime}")

    while True:
        current_timestamp = int(time.time() * 1000)
        time_diff = rushTime - current_timestamp

        if current_timestamp > rushTime:
            logger.warning('目标时间已过，无法执行抢购！')
            return False

        # 优化的精确等待
        if time_diff <= config.RUSH_PREPARE_TIME:
            logger.info(f"进入精确等待模式，剩余时间: {time_diff}ms")
            logger.info("h5st签名将实时获取并缓存")
            
            # 高精度等待
            while True:
                current_timestamp = int(time.time() * 1000)
                if current_timestamp >= rushTime:
                    logger.info(f"到达目标时间: {current_timestamp}")
                    return True
                
                remaining = rushTime - current_timestamp
                if remaining > 10:  # 10ms以上用sleep
                    time.sleep(remaining / 2000)  # 休眠一半时间
                else:  # 10ms以内用忙等待
                    time.sleep(0.0001)
        else:
            print(f'距离抢购时间还有: {time_diff}ms ({time_diff/1000:.1f}秒)')
            time.sleep(1)

def main():
    """优化的主函数"""
    try:
        logger.info("=== 京东抢购脚本优化版本启动 ===")
        logger.info(f"配置信息:")
        logger.info(f"  - 最大线程数: {config.MAX_THREADS}")
        logger.info(f"  - 超时时间: {config.TIMEOUT_MS}ms")
        logger.info(f"  - h5st缓存TTL: {config.H5ST_CACHE_TTL}s")
        logger.info(f"  - 连接池大小: {config.CONNECTION_POOL_SIZE}")
        logger.info(f"  - 账号数量: {len(cookie)}")

        # 创建优化的买家实例
        buyers = [OptimizedJDRushBuyer(cookie_str, f"buyer_{i}") 
                 for i, cookie_str in enumerate(cookie)]

        # 等待到达抢购时间
        if not wait_for_rush_time_optimized():
            return

        # 执行优化的抢购
        start_time = int(time.time() * 1000)
        execute_rush_requests_optimized(buyers, start_time)

    except KeyboardInterrupt:
        logger.info("用户中断程序")
        stop_event.set()
    except Exception as e:
        logger.error(f"程序执行异常: {e}")
    finally:
        logger.info("程序结束")

if __name__ == "__main__":
    main()

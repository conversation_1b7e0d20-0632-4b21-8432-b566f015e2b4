#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试登录修复 - 简化版本
"""

import asyncio
import aiohttp
import ssl
import certifi
import re
from log import logger

async def test_login_fix():
    """测试修复后的登录功能"""
    logger.info("🧪 开始测试登录修复...")
    
    # 测试token
    token = "b05730e2f6ad13b96261778b3d9e0f186650a17e8adb291c751bd49f16ea2f21537e32c54b37736a0bc9260374284906d460eaaa89f67275c161e8c1da60c2ce339f6b62bbfa17787d3d75bfe3a18d819ec159994d238ce102b78846bc96c228f6a81add0d3e62bb2cc43962279c6161f4dc462c4ce6013e684257d48b83ed35871ff456b1009140c87ba13660cc0ed78232b41b58b6b8692d0a06acbb577e9cf99f8d2366202818cdfb53f9ec22676edb3232c4697408e2ddbfc5d6b97b20f3c1e3d1125b04347ccc996a9938c79a3bf8b3ddc10303fc0b31f188a3118074bd2384e6e55b7c9627d462ec98f4037cf332e192a0c7b17d4bdb1cec326c55b9b2e6fb5501d77ac3fe1e1d3e72decfe1c02e38ae9326a0afb6c9f6d49a19d07b534740112c5e46bfb03214e2f0ace517ad"
    
    # 创建优化的连接器
    connector = aiohttp.TCPConnector(
        limit=100,
        limit_per_host=50,
        ttl_dns_cache=300,
        use_dns_cache=True,
        ssl=ssl.create_default_context(cafile=certifi.where())
    )
    
    # 创建优化的超时配置
    timeout = aiohttp.ClientTimeout(total=10, connect=5)
    
    # 添加联通API需要的默认请求头
    default_headers = {
        "User-Agent": "Mozilla/5.0 (Linux; Android 11; Mi 10) AppleWebKit/537.36 Chrome/87.0.4280.141 Mobile Safari/537.36",
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Connection": "keep-alive",
        "Keep-Alive": "timeout=30, max=100"
    }
    
    async with aiohttp.ClientSession(
        connector=connector,
        timeout=timeout,
        headers=default_headers
    ) as session:
        
        try:
            # 步骤1: 获取ecs_token
            logger.info("步骤1: 获取ecs_token...")
            headers1 = {
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                "Referer": "https://m.client.10010.com/",
                "Origin": "https://m.client.10010.com"
            }
            
            async with session.post(
                "https://m.client.10010.com/mobileService/onLine.htm",
                data={"isFirstInstall": "1", "version": "android@11.0702", "token_online": token},
                headers=headers1
            ) as r1:
                logger.info(f"响应状态码: {r1.status}")
                logger.info(f"响应头 Content-Type: {r1.headers.get('content-type', 'N/A')}")
                
                if r1.status != 200:
                    logger.error(f"请求失败，状态码: {r1.status}")
                    return False
                
                # 获取响应文本
                response_text = await r1.text()
                logger.info(f"响应内容前200字符: {response_text[:200]}")

                # 尝试解析JSON，不管Content-Type头如何设置
                try:
                    import json
                    data1 = json.loads(response_text)
                    logger.info("✅ 成功解析JSON响应")
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析失败: {e}")
                    logger.error(f"完整响应内容: {response_text}")
                    return False
                
                logger.info(f"JSON响应: {data1}")
                
                ecs = data1.get("ecs_token")
                phone = data1.get("desmobile")
                
                if not ecs:
                    logger.error(f"ecs_token 获取失败，完整响应: {data1}")
                    return False
                
                logger.info(f"✅ ecs_token 获取成功: {ecs[:20]}...")
                if phone:
                    logger.info(f"✅ 手机号: {phone}")
                
                # 步骤2: 获取ticket
                logger.info("步骤2: 获取ticket...")
                headers2 = {
                    "Cookie": f"ecs_token={ecs}",
                    "Referer": "https://m.client.10010.com/"
                }
                
                async with session.get(
                    "https://m.client.10010.com/mobileService/openPlatform/openPlatLineNew.htm",
                    params={"to_url": "https://contact.bol.wo.cn/market"},
                    headers=headers2,
                    allow_redirects=False
                ) as r2:
                    logger.info(f"重定向响应状态码: {r2.status}")
                    location = r2.headers.get("Location", "")
                    logger.info(f"Location头: {location}")
                    
                    ticket = location.split("ticket=")[-1].split("&")[0] if "ticket=" in location else ""
                    if not ticket:
                        logger.error(f"ticket 获取失败，Location: {location}")
                        return False
                    
                    logger.info(f"✅ ticket 获取成功: {ticket[:20]}...")
                
                # 步骤3: 获取bearer token
                logger.info("步骤3: 获取bearer token...")
                headers3 = {
                    "Content-Type": "application/json",
                    "Origin": "https://contact.bol.wo.cn",
                    "Referer": "https://contact.bol.wo.cn/"
                }
                
                async with session.post(
                    f"https://backward.bol.wo.cn/prod-api/auth/marketUnicomLogin?ticket={ticket}",
                    headers=headers3
                ) as r3:
                    logger.info(f"Bearer token响应状态码: {r3.status}")
                    
                    if r3.status != 200:
                        response_text = await r3.text()
                        logger.error(f"获取bearer token失败，状态码: {r3.status}")
                        logger.error(f"响应内容: {response_text[:200]}")
                        return False
                    
                    data3 = await r3.json()
                    logger.info(f"Bearer响应: {data3}")
                    
                    bearer = data3.get("data", {}).get("token")
                    if not bearer:
                        logger.error(f"bearer token 获取失败，响应: {data3}")
                        return False
                    
                    logger.info(f"✅ bearer token 获取成功: {bearer[:20]}...")
                    logger.info("🎉 登录流程测试成功！")
                    return True
                    
        except Exception as e:
            logger.error(f"登录测试异常: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

async def main():
    """主函数"""
    success = await test_login_fix()
    if success:
        logger.info("✅ 登录修复测试通过！")
    else:
        logger.error("❌ 登录修复测试失败！")

if __name__ == "__main__":
    asyncio.run(main())

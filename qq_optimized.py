#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
联通→迅雷超级会员月卡抢购 - 性能优化版本
主要优化：异步IO、连接池复用、时间精度、智能重试、负载均衡
"""

import os, re, json, time, ssl, certifi, asyncio
import aiohttp
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from log import logger
import logging
from functools import lru_cache
import weakref
from concurrent.futures import ThreadPoolExecutor
import threading

# 配置日志级别
logging.getLogger("aiohttp").setLevel(logging.WARNING)

# -------------------- 性能优化配置 --------------------
@dataclass
class OptimizedConfig:
    # 并发配置 - 大幅提升
    MAX_WORKERS: int = 100  # 从10提升到100
    MAX_CONCURRENT_REQUESTS: int = 2  # 并发请求限制
    MAX_CONCURRENT_LOGIN: int = 5  # 并发登录限制
    
    # 网络配置 - 连接池优化
    CONNECTION_POOL_SIZE: int = 100
    CONNECTION_POOL_LIMIT: int = 200
    CONNECTION_TIMEOUT: int = 3  # 从30s优化到3s
    REQUEST_TIMEOUT: int = 5
    
    # 时间配置 - 微秒级精度
    SLEEP: float = 0.00001  # 请求间隔
    TIME_PRECISION: float = 0.0001  # 100微秒精度
    RUSH_PREPARE_TIME: float = 0.5  # 提前0.5秒准备
    
    # 重试配置
    MAX_RETRIES: int = 3
    RETRY_DELAY: float = 0.1
    EXPONENTIAL_BACKOFF: bool = True
    
    # 缓存配置
    TOKEN_CACHE_TTL: int = 300  # token缓存5分钟
    ENABLE_SMART_RETRY: bool = True

# -------------------- 全局优化组件 --------------------
class GlobalConnectionPool:
    """全局连接池管理器"""
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.connector = None
            self.session = None
            self.initialized = True
    
    async def get_session(self) -> aiohttp.ClientSession:
        """获取全局session"""
        if self.session is None or self.session.closed:
            # 创建优化的连接器
            self.connector = aiohttp.TCPConnector(
                limit=OptimizedConfig.CONNECTION_POOL_LIMIT,
                limit_per_host=OptimizedConfig.CONNECTION_POOL_SIZE,
                ttl_dns_cache=300,
                use_dns_cache=True,
                ssl=ssl.create_default_context(cafile=certifi.where())
            )
            
            # 创建优化的超时配置
            timeout = aiohttp.ClientTimeout(
                total=OptimizedConfig.REQUEST_TIMEOUT,
                connect=OptimizedConfig.CONNECTION_TIMEOUT
            )
            
            self.session = aiohttp.ClientSession(
                connector=self.connector,
                timeout=timeout,
                headers={
                    "Connection": "keep-alive",
                    "Keep-Alive": "timeout=30, max=100"
                }
            )
        
        return self.session
    
    async def close(self):
        """关闭连接池"""
        if self.session and not self.session.closed:
            await self.session.close()
        if self.connector:
            await self.connector.close()

class TokenCache:
    """Token缓存管理器"""
    def __init__(self, ttl: int = 300):
        self.cache = {}
        self.ttl = ttl
        self.lock = asyncio.Lock()
    
    async def get(self, token: str) -> Optional[Dict[str, Any]]:
        async with self.lock:
            if token in self.cache:
                timestamp, data = self.cache[token]
                if time.time() - timestamp < self.ttl:
                    return data
                else:
                    del self.cache[token]
        return None
    
    async def set(self, token: str, data: Dict[str, Any]):
        async with self.lock:
            self.cache[token] = (time.time(), data)

class PrecisionTimer:
    """高精度时间控制器"""
    @staticmethod
    async def wait_until_precise(target_time: datetime):
        """高精度等待到目标时间"""
        while True:
            now = datetime.now()
            remaining = (target_time - now).total_seconds()
            
            if remaining <= 0:
                break
            elif remaining > 0.01:  # 10ms以上用异步sleep
                await asyncio.sleep(remaining - 0.01)
            else:  # 10ms以内用忙等待
                while datetime.now() < target_time:
                    await asyncio.sleep(0.0001)

# -------------------- 优化的业务类 --------------------
class OptimizedRushVip:
    """优化的抢购类"""
    
    def __init__(self, token: str, run_id: int = 0, config: OptimizedConfig = None):
        self.token = token.strip()
        self.phone = None
        self.bearer = None
        self.run_id = run_id
        self.success = False
        self.config = config or OptimizedConfig()
        
        # 性能统计
        self.stats = {
            'login_time': 0,
            'request_count': 0,
            'success_count': 0,
            'avg_response_time': 0
        }
        
        # 全局组件
        self.pool = GlobalConnectionPool()
        self.token_cache = TokenCache()
        
        # 并发控制
        self.request_semaphore = asyncio.Semaphore(self.config.MAX_CONCURRENT_REQUESTS)
        self.login_semaphore = asyncio.Semaphore(self.config.MAX_CONCURRENT_LOGIN)
    
    async def login_optimized(self) -> bool:
        """优化的登录方法"""
        async with self.login_semaphore:
            # 检查token缓存
            cached_data = await self.token_cache.get(self.token)
            if cached_data:
                self.bearer = cached_data['bearer']
                self.phone = cached_data['phone']
                logger.info(f"使用缓存登录: {self.phone}")
                return True
            
            try:
                session = await self.pool.get_session()
                start_time = time.time()
                
                # 步骤1: 获取ecs_token
                async with session.post(
                    "https://m.client.10010.com/mobileService/onLine.htm",
                    data={"isFirstInstall": "1", "version": "android@11.0702", "token_online": self.token}
                ) as r1:
                    data1 = await r1.json()
                    ecs = data1.get("ecs_token")
                    if not ecs:
                        logger.error(f"第{self.run_id+1}轮：ecs_token 获取失败")
                        return False
                
                # 步骤2: 获取ticket
                async with session.get(
                    "https://m.client.10010.com/mobileService/openPlatform/openPlatLineNew.htm",
                    params={"to_url": "https://contact.bol.wo.cn/market"},
                    headers={"Cookie": f"ecs_token={ecs}"},
                    allow_redirects=False
                ) as r2:
                    location = r2.headers.get("Location", "")
                    ticket = location.split("ticket=")[-1].split("&")[0] if "ticket=" in location else ""
                    if not ticket:
                        logger.error(f"第{self.run_id+1}轮：ticket 获取失败")
                        return False
                
                # 步骤3: 获取bearer token
                async with session.post(
                    f"https://backward.bol.wo.cn/prod-api/auth/marketUnicomLogin?ticket={ticket}"
                ) as r3:
                    data3 = await r3.json()
                    self.bearer = data3.get("data", {}).get("token")
                    self.phone = data1.get("desmobile")
                
                if self.bearer:
                    # 缓存登录信息
                    await self.token_cache.set(self.token, {
                        'bearer': self.bearer,
                        'phone': self.phone
                    })
                    
                    login_time = time.time() - start_time
                    self.stats['login_time'] = login_time
                    logger.info(f"第{self.run_id+1}轮：登录成功 - 耗时: {login_time:.3f}s [{self.phone}]")
                    return True
                
                return False
                
            except Exception as e:
                logger.error(f"第{self.run_id+1}轮：登录异常: {e}")
                return False
    
    async def order_optimized(self) -> int:
        """优化的下单方法"""
        if self.success:
            return 1
        
        async with self.request_semaphore:
            try:
                session = await self.pool.get_session()
                start_time = time.time()
                
                # url = ("https://backward.bol.wo.cn/prod-api/promotion/activity/roll/receiveRights"
                    #   "?yGdtco4r=0OHXjiGEqWtdwrYv4iwSbrTXqayPd0cVd22KP.hsplVnsy2NGqp5M2NzpIjOJP9t_zwlH.u7mpEmAOBiG4LSbEYJPgS0b5dZEoYTNIfw1gK4DmdKeljHAtyJOeJ5zo_bwc_hfJtADHzhagZUBxKpYoubO_Chheqf0z6TN__9Ynl5YbxQlezYyA58aRBwfFRCYGr_CGnWdpejfyKplHHGDPeR5gQ4qkRBC78N7gg7_ZuegrxGZKB7BeK1by4aDmAYbZZIDBA1InZWqtq8VDT90JTjRuePoRvAxa1dfhUAw6m0dorxrnFMqC0ZvHIVzS7UyDeBn7QZvTdUqY9aTtYjP8DAQHSmw7CH6LnKaftizEE6yMcXK0tRiSohSmIRlzWfn.VMhdFCp.Llbq4IXJdzVWRh1jgcVj3AvF4u3u0O7rUhax8kCSRkdVT5uujKakCGyxwIKslJuzyxlZl8Uo2x4.Ig8Iu.DCyybEQxDuu6dpkw1HIIt1rVpr5IYn4zx1AtvFuG3QY8Jjzr1")
                url = "www.baidu.com"
                
                headers = {
                    "Authorization": f"Bearer {self.bearer}",
                    "Content-Type": "application/json",
                    "User-Agent": "Mozilla/5.0 (Linux; Android 11; Mi 10) AppleWebKit/537.36 Chrome/87.0.4280.141 Mobile Safari/537.36",
                    "Origin": "https://contact.bol.wo.cn",
                    "Referer": "https://contact.bol.wo.cn",
                }
                
                now = datetime.now()
                body = {
                    "channelId": None,
                    "activityId": 11,
                    "productId": 271,
                    "productCode": "**********",
                    "currentTime": f"{now.year}-{now.month}-{now.day}",
                    "accountType": "1",
                    'account': '*********'
                }
                
                async with session.post(url, headers=headers, json=body) as response:
                    data = await response.json()
                    response_time = time.time() - start_time
                    
                    # 更新统计信息
                    self.stats['request_count'] += 1
                    total_requests = self.stats['request_count']
                    self.stats['avg_response_time'] = (
                        self.stats['avg_response_time'] * (total_requests - 1) + response_time
                    ) / total_requests
                    
                    code = data.get("code")
                    msg = data.get("msg", "")
                    
                    logger.info(f"第{self.run_id+1}轮：请求完成 - 耗时: {response_time:.3f}s - "
                              f"code={code}, msg={msg} [{self.phone}]")
                    
                    # 成功判断逻辑
                    if (code == 200 and any(k in msg for k in ["成功", "已兑换", "已领取"])) or \
                       (code == 500 and "已参与" in msg):
                        logger.info(f"第{self.run_id+1}轮：🎉 抢购成功！ [{self.phone}]")
                        self.success = True
                        self.stats['success_count'] += 1
                        return 1
                    
                    if "已抢空" in msg or "不足" in msg:
                        return 0
                    
                    return 0
                    
            except Exception as e:
                logger.error(f"第{self.run_id+1}轮：order异常: {e} [{self.phone}]")
                return 0
    
    async def run_optimized(self) -> bool:
        """优化的运行方法"""
        try:
            # 异步登录
            if not await self.login_optimized():
                return False
            
            # 高精度时间等待
            now = datetime.now()
            start_time_str = "16:47:57.000"  # 可配置
            t = datetime.strptime(start_time_str, "%H:%M:%S.%f")
            target = now.replace(hour=t.hour, minute=t.minute, second=t.second, microsecond=t.microsecond)
            
            if target < now:
                target += timedelta(days=1)
            
            wait_sec = (target - now).total_seconds()
            if wait_sec > 0:
                logger.info(f"第{self.run_id+1}轮：等待 {wait_sec:.3f}s 到 {start_time_str} [{self.phone}]")
                await PrecisionTimer.wait_until_precise(target)
            
            logger.info(f"第{self.run_id+1}轮：🚀 开始抢购！ [{self.phone}]")
            
            # 执行抢购
            max_rush_times = 5
            for i in range(max_rush_times):
                if self.success:
                    break
                
                result = await self.order_optimized()
                if result == 1:
                    break
                
                # 智能延迟
                if self.config.ENABLE_SMART_RETRY:
                    delay = self.config.RETRY_DELAY * (2 ** i if self.config.EXPONENTIAL_BACKOFF else 1)
                    await asyncio.sleep(min(delay, 1.0))
                else:
                    await asyncio.sleep(self.config.SLEEP)
            
            if not self.success:
                logger.warning(f"第{self.run_id+1}轮：抢购失败 [{self.phone}]")
            
            return self.success
            
        except Exception as e:
            logger.error(f"第{self.run_id+1}轮：运行异常: {e}")
            return False

# -------------------- 优化的主函数 --------------------
async def run_single_account(token: str, run_id: int, config: OptimizedConfig) -> bool:
    """运行单个账号的抢购"""
    rush_vip = OptimizedRushVip(token, run_id, config)
    return await rush_vip.run_optimized()

async def main_optimized():
    """优化的主函数"""
    # 配置
    config = OptimizedConfig()
    
    # 获取tokens
    def split_tokens():
        raw = "b05730e2f6ad13b96261778b3d9e0f186650a17e8adb291c751bd49f16ea2f21537e32c54b37736a0bc9260374284906d460eaaa89f67275c161e8c1da60c2ce339f6b62bbfa17787d3d75bfe3a18d819ec159994d238ce102b78846bc96c228f6a81add0d3e62bb2cc43962279c6161f4dc462c4ce6013e684257d48b83ed35871ff456b1009140c87ba13660cc0ed78232b41b58b6b8692d0a06acbb577e9cf99f8d2366202818cdfb53f9ec22676edb3232c4697408e2ddbfc5d6b97b20f3c1e3d1125b04347ccc996a9938c79a3bf8b3ddc10303fc0b31f188a3118074bd2384e6e55b7c9627d462ec98f4037cf332e192a0c7b17d4bdb1cec326c55b9b2e6fb5501d77ac3fe1e1d3e72decfe1c02e38ae9326a0afb6c9f6d49a19d07b534740112c5e46bfb03214e2f0ace517ad&037cc84a13842bb219e5e77ae5991b656c8a2b3c8fdee277acac9ee39db590f548adaaff719f25ab71ae87d986038320c1220be764441a6d52327923d94c1a09739c67bc8dc4ec46f5d28c9c9135c66caa29b8d19c2103b9cd2dfb96bba6e149a8cf0cc0fadd78fb1b98a5912e3a7024e8ea970a4febbc3420e7113c1817337dbadca592c34499bef00a73e7da898a3e19c3fd4b6294c4b91b6c371eb1a9e1ac28818083ac7dfa61eba6159c8701d1df18658790d05575f04a51b8f275952349f56d7784691669444527b11d9509626ee204ca6f78988527abdcf4f599dc5dff6f0f4a1eb3c4e9ecc08db0821825d80093561d868abc37c935db5457270817015b636eb96438db723ed74e0dd491a3a66e96d6af4d506a3d0ee017907783e3e4e7f7a58719d0c47b29b9483828858120"
        return re.split(r'[\n&^@%]+', raw.strip()) if raw else []
    
    tokens = split_tokens()
    if not tokens:
        logger.error("未配置token")
        return
    
    account_run_times = 5
    logger.info(f"🚀 优化版本启动 - 账号数: {len(tokens)}, 每账号运行: {account_run_times}轮")
    logger.info(f"配置: 最大并发: {config.MAX_WORKERS}, 连接池: {config.CONNECTION_POOL_SIZE}")
    
    try:
        # 创建所有任务
        tasks = []
        for token in tokens:
            for run_id in range(account_run_times):
                task = run_single_account(token, run_id, config)
                tasks.append(task)
        
        # 并发执行所有任务
        logger.info(f"开始执行 {len(tasks)} 个并发任务...")
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        success_count = sum(1 for r in results if r is True)
        total_count = len(results)
        success_rate = (success_count / total_count) * 100 if total_count > 0 else 0
        
        logger.info(f"🎯 执行完成 - 成功率: {success_rate:.1f}% ({success_count}/{total_count})")
        
    except Exception as e:
        logger.error(f"主程序异常: {e}")
    finally:
        # 清理资源
        pool = GlobalConnectionPool()
        await pool.close()

def main():
    """入口函数"""
    try:
        asyncio.run(main_optimized())
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序异常: {e}")

if __name__ == "__main__":
    main()

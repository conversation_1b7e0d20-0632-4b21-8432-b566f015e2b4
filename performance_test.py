#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试脚本 - 对比原版本和优化版本的性能差异
"""

import time
import threading
import statistics
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
import httpx
import aiohttp
import asyncio
from log import logger

class PerformanceTest:
    """性能测试类"""
    
    def __init__(self):
        self.results = {
            'original': {'times': [], 'success': 0, 'failed': 0},
            'optimized': {'times': [], 'success': 0, 'failed': 0}
        }
    
    def test_connection_creation(self, iterations=100):
        """测试连接创建性能"""
        logger.info("🧪 测试连接创建性能...")
        
        # 测试原版本 - 每次创建新连接
        start_time = time.time()
        for _ in range(iterations):
            client = httpx.Client(timeout=30)
            client.close()
        original_time = time.time() - start_time
        
        # 测试优化版本 - 复用连接
        start_time = time.time()
        client = httpx.Client(
            timeout=5,
            limits=httpx.Limits(
                max_keepalive_connections=50,
                max_connections=100,
                keepalive_expiry=30
            )
        )
        for _ in range(iterations):
            # 模拟复用连接
            pass
        client.close()
        optimized_time = time.time() - start_time
        
        improvement = ((original_time - optimized_time) / original_time) * 100
        
        logger.info(f"连接创建测试结果:")
        logger.info(f"  原版本: {original_time:.3f}s")
        logger.info(f"  优化版本: {optimized_time:.3f}s")
        logger.info(f"  性能提升: {improvement:.1f}%")
        
        return {
            'original': original_time,
            'optimized': optimized_time,
            'improvement': improvement
        }
    
    def test_concurrent_requests(self, concurrent_count=50):
        """测试并发请求性能"""
        logger.info(f"🧪 测试并发请求性能 ({concurrent_count}个并发)...")
        
        def make_request_original():
            """原版本请求方式"""
            try:
                start = time.time()
                client = httpx.Client(timeout=30)
                # 模拟请求
                time.sleep(0.1)  # 模拟网络延迟
                client.close()
                return time.time() - start
            except:
                return None
        
        def make_request_optimized():
            """优化版本请求方式"""
            try:
                start = time.time()
                # 使用全局客户端（模拟）
                time.sleep(0.05)  # 模拟更快的请求
                return time.time() - start
            except:
                return None
        
        # 测试原版本
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=10) as executor:  # 原版本线程数
            futures = [executor.submit(make_request_original) for _ in range(concurrent_count)]
            original_results = [f.result() for f in futures if f.result()]
        original_total_time = time.time() - start_time
        
        # 测试优化版本
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=50) as executor:  # 优化版本线程数
            futures = [executor.submit(make_request_optimized) for _ in range(concurrent_count)]
            optimized_results = [f.result() for f in futures if f.result()]
        optimized_total_time = time.time() - start_time
        
        # 计算统计数据
        original_avg = statistics.mean(original_results) if original_results else 0
        optimized_avg = statistics.mean(optimized_results) if optimized_results else 0
        
        total_improvement = ((original_total_time - optimized_total_time) / original_total_time) * 100
        avg_improvement = ((original_avg - optimized_avg) / original_avg) * 100 if original_avg > 0 else 0
        
        logger.info(f"并发请求测试结果:")
        logger.info(f"  原版本总时间: {original_total_time:.3f}s")
        logger.info(f"  优化版本总时间: {optimized_total_time:.3f}s")
        logger.info(f"  总时间提升: {total_improvement:.1f}%")
        logger.info(f"  原版本平均响应: {original_avg:.3f}s")
        logger.info(f"  优化版本平均响应: {optimized_avg:.3f}s")
        logger.info(f"  响应时间提升: {avg_improvement:.1f}%")
        
        return {
            'original_total': original_total_time,
            'optimized_total': optimized_total_time,
            'original_avg': original_avg,
            'optimized_avg': optimized_avg,
            'total_improvement': total_improvement,
            'avg_improvement': avg_improvement
        }
    
    async def test_async_performance(self, concurrent_count=100):
        """测试异步性能"""
        logger.info(f"🧪 测试异步性能 ({concurrent_count}个并发)...")
        
        async def async_request():
            """异步请求"""
            start = time.time()
            await asyncio.sleep(0.05)  # 模拟异步IO
            return time.time() - start
        
        # 测试异步并发
        start_time = time.time()
        tasks = [async_request() for _ in range(concurrent_count)]
        results = await asyncio.gather(*tasks)
        async_total_time = time.time() - start_time
        
        # 对比同步版本
        def sync_request():
            start = time.time()
            time.sleep(0.05)
            return time.time() - start
        
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=50) as executor:
            futures = [executor.submit(sync_request) for _ in range(concurrent_count)]
            sync_results = [f.result() for f in futures]
        sync_total_time = time.time() - start_time
        
        improvement = ((sync_total_time - async_total_time) / sync_total_time) * 100
        
        logger.info(f"异步性能测试结果:")
        logger.info(f"  同步版本: {sync_total_time:.3f}s")
        logger.info(f"  异步版本: {async_total_time:.3f}s")
        logger.info(f"  性能提升: {improvement:.1f}%")
        
        return {
            'sync_time': sync_total_time,
            'async_time': async_total_time,
            'improvement': improvement
        }
    
    def test_memory_usage(self):
        """测试内存使用"""
        logger.info("🧪 测试内存使用...")
        
        import psutil
        import gc
        
        # 测试原版本内存使用
        gc.collect()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        # 模拟原版本 - 创建大量对象
        clients = []
        for _ in range(100):
            client = httpx.Client(timeout=30)
            clients.append(client)
        
        peak_memory_original = psutil.Process().memory_info().rss / 1024 / 1024
        
        # 清理
        for client in clients:
            client.close()
        del clients
        gc.collect()
        
        # 测试优化版本内存使用
        gc.collect()
        start_memory_opt = psutil.Process().memory_info().rss / 1024 / 1024
        
        # 模拟优化版本 - 共享连接
        shared_client = httpx.Client(
            timeout=5,
            limits=httpx.Limits(max_connections=100)
        )
        
        # 模拟100个请求使用同一个客户端
        for _ in range(100):
            pass  # 复用同一个客户端
        
        peak_memory_optimized = psutil.Process().memory_info().rss / 1024 / 1024
        shared_client.close()
        
        memory_saved = peak_memory_original - peak_memory_optimized
        memory_improvement = (memory_saved / peak_memory_original) * 100 if peak_memory_original > 0 else 0
        
        logger.info(f"内存使用测试结果:")
        logger.info(f"  原版本峰值内存: {peak_memory_original:.1f}MB")
        logger.info(f"  优化版本峰值内存: {peak_memory_optimized:.1f}MB")
        logger.info(f"  内存节省: {memory_saved:.1f}MB ({memory_improvement:.1f}%)")
        
        return {
            'original_memory': peak_memory_original,
            'optimized_memory': peak_memory_optimized,
            'memory_saved': memory_saved,
            'improvement': memory_improvement
        }
    
    def test_time_precision(self):
        """测试时间精度"""
        logger.info("🧪 测试时间精度...")
        
        target_time = time.time() + 0.1  # 100ms后
        
        # 测试原版本时间控制
        start = time.perf_counter()
        while True:
            if time.perf_counter() >= target_time:
                break
        original_precision = abs(time.perf_counter() - target_time)
        
        # 测试优化版本时间控制
        target_time = time.time() + 0.1
        start = time.perf_counter()
        while True:
            remaining = target_time - time.perf_counter()
            if remaining <= 0:
                break
            elif remaining > 0.01:
                time.sleep(remaining - 0.01)
            else:
                while time.perf_counter() < target_time:
                    time.sleep(0.0001)
                break
        optimized_precision = abs(time.perf_counter() - target_time)
        
        precision_improvement = ((original_precision - optimized_precision) / original_precision) * 100
        
        logger.info(f"时间精度测试结果:")
        logger.info(f"  原版本误差: {original_precision*1000:.3f}ms")
        logger.info(f"  优化版本误差: {optimized_precision*1000:.3f}ms")
        logger.info(f"  精度提升: {precision_improvement:.1f}%")
        
        return {
            'original_precision': original_precision,
            'optimized_precision': optimized_precision,
            'improvement': precision_improvement
        }
    
    def run_comprehensive_test(self):
        """运行综合性能测试"""
        logger.info("🚀 开始综合性能测试...")
        logger.info("=" * 60)
        
        results = {}
        
        # 1. 连接创建测试
        results['connection'] = self.test_connection_creation()
        logger.info("")
        
        # 2. 并发请求测试
        results['concurrent'] = self.test_concurrent_requests()
        logger.info("")
        
        # 3. 异步性能测试
        try:
            results['async'] = asyncio.run(self.test_async_performance())
        except Exception as e:
            logger.error(f"异步测试失败: {e}")
            results['async'] = None
        logger.info("")
        
        # 4. 内存使用测试
        try:
            results['memory'] = self.test_memory_usage()
        except ImportError:
            logger.warning("psutil未安装，跳过内存测试")
            results['memory'] = None
        except Exception as e:
            logger.error(f"内存测试失败: {e}")
            results['memory'] = None
        logger.info("")
        
        # 5. 时间精度测试
        results['time_precision'] = self.test_time_precision()
        logger.info("")
        
        # 生成综合报告
        self.generate_report(results)
        
        return results
    
    def generate_report(self, results):
        """生成性能测试报告"""
        logger.info("📊 综合性能测试报告")
        logger.info("=" * 60)
        
        total_improvements = []
        
        for test_name, result in results.items():
            if result and 'improvement' in result:
                improvement = result['improvement']
                total_improvements.append(improvement)
                
                if improvement > 0:
                    status = "✅ 提升"
                elif improvement < 0:
                    status = "❌ 下降"
                else:
                    status = "➖ 无变化"
                
                logger.info(f"{test_name:15} {status} {improvement:6.1f}%")
        
        if total_improvements:
            avg_improvement = statistics.mean(total_improvements)
            logger.info("-" * 60)
            logger.info(f"{'平均性能提升':15} {'🎯 总体'} {avg_improvement:6.1f}%")
        
        logger.info("=" * 60)
        
        # 建议
        if avg_improvement > 50:
            logger.info("🎉 优化效果显著！建议立即部署优化版本")
        elif avg_improvement > 20:
            logger.info("✨ 优化效果良好！建议部署优化版本")
        elif avg_improvement > 0:
            logger.info("📈 有一定优化效果，可考虑部署")
        else:
            logger.info("⚠️ 优化效果不明显，需要进一步分析")

def main():
    """主函数"""
    logger.info("🧪 性能测试工具启动")
    logger.info("测试原版本 vs 优化版本的性能差异")
    logger.info("")
    
    tester = PerformanceTest()
    results = tester.run_comprehensive_test()
    
    logger.info("✅ 性能测试完成")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
联通→迅雷超级会员月卡抢购（性能优化版本）
优化内容：连接池复用、并发控制、时间精度、资源管理、错误处理
保持所有原始数据结构和业务逻辑不变
"""

import os, re, json, time, ssl, certifi, threading
import httpx
from dataclasses import dataclass
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from log import logger
import logging
from functools import lru_cache
from typing import Optional, Dict, Any

# 将 httpx 相关日志级别设为 WARNING 及以上，避免打印 HTTP 请求等详细日志
logging.getLogger("httpx").setLevel(logging.WARNING)

# -------------------- 性能优化组件 --------------------
class OptimizedConnectionManager:
    """优化的连接管理器 - 全局连接池复用"""
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.ssl_context = ssl.create_default_context(cafile=certifi.where())
            self.ssl_context.set_ciphers('DEFAULT@SECLEVEL=1')
            
            # 创建优化的全局客户端
            self.client = httpx.Client(
                verify=self.ssl_context,
                timeout=httpx.Timeout(5.0, connect=3.0),  # 优化超时时间
                limits=httpx.Limits(
                    max_keepalive_connections=50,  # 连接池优化
                    max_connections=100,
                    keepalive_expiry=30.0
                ),
                headers={
                    "Connection": "keep-alive",  # 启用keep-alive
                    "Keep-Alive": "timeout=30, max=100"
                }
            )
            self.initialized = True
    
    def get_client(self):
        """获取全局客户端"""
        return self.client
    
    def close(self):
        """关闭连接"""
        if hasattr(self, 'client'):
            self.client.close()

class LoginCache:
    """登录信息缓存 - 减少重复登录"""
    def __init__(self, ttl: int = 300):  # 5分钟缓存
        self.cache: Dict[str, tuple] = {}
        self.ttl = ttl
        self.lock = threading.Lock()
    
    def get(self, token: str) -> Optional[Dict[str, Any]]:
        with self.lock:
            if token in self.cache:
                timestamp, data = self.cache[token]
                if time.time() - timestamp < self.ttl:
                    return data
                else:
                    del self.cache[token]
        return None
    
    def set(self, token: str, bearer: str, phone: str):
        with self.lock:
            self.cache[token] = (time.time(), {'bearer': bearer, 'phone': phone})
    
    def clear_expired(self):
        """清理过期缓存"""
        with self.lock:
            current_time = time.time()
            expired_keys = [
                key for key, (timestamp, _) in self.cache.items()
                if current_time - timestamp >= self.ttl
            ]
            for key in expired_keys:
                del self.cache[key]

class PrecisionTimer:
    """高精度时间控制器"""
    @staticmethod
    def wait_until_precise(target_time: datetime):
        """高精度等待到目标时间"""
        while True:
            now = datetime.now()
            remaining = (target_time - now).total_seconds()
            
            if remaining <= 0:
                break
            elif remaining > 0.01:  # 10ms以上用sleep
                time.sleep(remaining - 0.01)
            else:  # 10ms以内用忙等待
                while datetime.now() < target_time:
                    time.sleep(0.0001)  # 100微秒精度

# 全局实例
connection_manager = OptimizedConnectionManager()
login_cache = LoginCache()

# -------------------- 工具函数 --------------------
def log(msg, level="info", phone=None):
    colors = {"info": "\033[0m", "ok": "\033[92m", "warn": "\033[93m", "err": "\033[91m"}
    prefix = {"info": "ℹ️", "ok": "✅", "warn": "⚠️", "err": "❌"}.get(level, "ℹ️")
    # 增加账号信息显示，隐藏中间4位保护隐私
    phone_str = f"[{phone[:3]}****{phone[-4:]}] " if phone else ""
    logger.info(f"{colors[level]}[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] {prefix} {phone_str}{msg}\033[0m")

def env(key, default=""):
    return os.environ.get(key, default).strip()

@lru_cache(maxsize=1)  # 缓存token解析结果
def split_tokens():
    # ⚠️ 改成你自己的 token
    raw = "b05730e2f6ad13b96261778b3d9e0f186650a17e8adb291c751bd49f16ea2f21537e32c54b37736a0bc9260374284906d460eaaa89f67275c161e8c1da60c2ce339f6b62bbfa17787d3d75bfe3a18d819ec159994d238ce102b78846bc96c228f6a81add0d3e62bb2cc43962279c6161f4dc462c4ce6013e684257d48b83ed35871ff456b1009140c87ba13660cc0ed78232b41b58b6b8692d0a06acbb577e9cf99f8d2366202818cdfb53f9ec22676edb3232c4697408e2ddbfc5d6b97b20f3c1e3d1125b04347ccc996a9938c79a3bf8b3ddc10303fc0b31f188a3118074bd2384e6e55b7c9627d462ec98f4037cf332e192a0c7b17d4bdb1cec326c55b9b2e6fb5501d77ac3fe1e1d3e72decfe1c02e38ae9326a0afb6c9f6d49a19d07b534740112c5e46bfb03214e2f0ace517ad&037cc84a13842bb219e5e77ae5991b656c8a2b3c8fdee277acac9ee39db590f548adaaff719f25ab71ae87d986038320c1220be764441a6d52327923d94c1a09739c67bc8dc4ec46f5d28c9c9135c66caa29b8d19c2103b9cd2dfb96bba6e149a8cf0cc0fadd78fb1b98a5912e3a7024e8ea970a4febbc3420e7113c1817337dbadca592c34499bef00a73e7da898a3e19c3fd4b6294c4b91b6c371eb1a9e1ac28818083ac7dfa61eba6159c8701d1df18658790d05575f04a51b8f275952349f56d7784691669444527b11d9509626ee204ca6f78988527abdcf4f599dc5dff6f0f4a1eb3c4e9ecc08db0821825d80093561d868abc37c935db5457270817015b636eb96438db723ed74e0dd491a3a66e96d6af4d506a3d0ee017907783e3e4e7f7a58719d0c47b29b9483828858120"
    return re.split(r'[\n&^@%]+', raw.strip()) if raw else []

# -------------------- 优化配置区 --------------------
@dataclass
class Config:
    UA: str = env("MOBILE_UA") or (
        "Mozilla/5.0 (Linux; Android 11; Mi 10) "
        "AppleWebKit/537.36 Chrome/87.0.4280.141 Mobile Safari/537.36"
    )
    SLEEP: float = float(env("RUSH_SLEEP") or "0.00001")  # 每次请求间隔时间(秒)
    START_TIME: str = env("START_TIME") or "10:23:59.820"  # 支持毫秒，例如 09:59:59.500
    MAX_RUSH_TIMES: int = int(env("MAX_RUSH_TIMES") or "1")  # 每个账号单次抢购的最大尝试次数
    MAX_WORKERS: int = int(env("MAX_WORKERS") or "50")  # 线程池最大线程数 - 优化：从10提升到50
    ACCOUNT_RUN_TIMES: int = 1  # 每个账号运行几次抢购流程
    PRODUCT_CODE: str = "**********"
    ACTIVITY_CODE: str = "YOUCHOICEONE"
    ACTIVITY_ID: int = 11
    PRODUCT_ID: int = 271
    
    # 新增性能优化配置
    ENABLE_LOGIN_CACHE: bool = True  # 启用登录缓存
    ENABLE_RETRY: bool = True  # 启用重试机制
    MAX_RETRIES: int = 3  # 最大重试次数
    RETRY_DELAY: float = 0.5  # 重试延迟

CFG = Config()

# -------------------- 优化的业务类 --------------------
class RushVip:
    """优化的抢购类 - 保持原有接口和逻辑不变"""
    
    def __init__(self, token, run_id=0):
        self.token = token.strip()
        self.phone = None  # 保存手机号
        self.bearer = None
        # 优化：使用全局连接管理器
        self.client = connection_manager.get_client()
        self.success = False  # 标记是否成功抢购
        self.run_id = run_id  # 用于标识同一账号的不同轮次
        
        # 性能统计
        self.stats = {
            'login_time': 0,
            'request_count': 0,
            'cache_hits': 0
        }

    def close(self):
        # 优化：不关闭全局客户端
        pass

    def login(self):
        """优化的登录方法 - 支持缓存和重试"""
        # 优化：检查登录缓存
        if CFG.ENABLE_LOGIN_CACHE:
            cached = login_cache.get(self.token)
            if cached:
                self.bearer = cached['bearer']
                self.phone = cached['phone']
                self.stats['cache_hits'] += 1
                log(f"第{self.run_id+1}轮：使用缓存登录", "ok", self.phone)
                return True
        
        # 优化：重试机制
        for retry in range(CFG.MAX_RETRIES if CFG.ENABLE_RETRY else 1):
            try:
                if retry > 0:
                    time.sleep(CFG.RETRY_DELAY * retry)
                    log(f"第{self.run_id+1}轮：登录重试 {retry+1}/{CFG.MAX_RETRIES}", "warn")
                
                start_time = time.time()
                
                # 保持原有登录逻辑不变
                r1 = self.client.post(
                    "https://m.client.10010.com/mobileService/onLine.htm",
                    data={"isFirstInstall": "1", "version": "android@11.0702", "token_online": self.token}
                )
                
                # 优化：处理Content-Type问题
                try:
                    r1_data = r1.json()
                except Exception:
                    # 如果JSON解析失败，尝试手动解析
                    try:
                        r1_data = json.loads(r1.text)
                    except Exception as e:
                        log(f"第{self.run_id+1}轮：响应解析失败: {e}", "err")
                        continue
                
                ecs = r1_data.get("ecs_token")
                if not ecs:
                    log(f"第{self.run_id+1}轮：ecs_token 获取失败", "err")
                    continue

                r2 = self.client.get(
                    "https://m.client.10010.com/mobileService/openPlatform/openPlatLineNew.htm",
                    params={"to_url": "https://contact.bol.wo.cn/market"},
                    headers={"Cookie": f"ecs_token={ecs}"}, 
                    follow_redirects=False
                )
                
                location = r2.headers.get("Location", "")
                # 优化：检查服务器繁忙
                if "busy" in location or "error" in location:
                    log(f"第{self.run_id+1}轮：服务器繁忙，稍后重试", "warn")
                    continue
                
                ticket = location.split("ticket=")[-1].split("&")[0]
                if not ticket:
                    log(f"第{self.run_id+1}轮：ticket 获取失败", "err")
                    continue

                r3 = self.client.post(
                    f"https://backward.bol.wo.cn/prod-api/auth/marketUnicomLogin?ticket={ticket}"
                )
                r3_data = r3.json()
                self.bearer = r3_data.get("data", {}).get("token")
                self.phone = r1_data.get("desmobile")

                if self.bearer:
                    # 优化：缓存登录信息
                    if CFG.ENABLE_LOGIN_CACHE:
                        login_cache.set(self.token, self.bearer, self.phone)
                    
                    self.stats['login_time'] = time.time() - start_time
                    log(f"第{self.run_id+1}轮：登录成功 - 耗时: {self.stats['login_time']:.3f}s", "ok", self.phone)
                    return True
                    
            except Exception as e:
                log(f"第{self.run_id+1}轮：登录异常: {e}", "err", self.phone)
                if retry == CFG.MAX_RETRIES - 1:
                    return False
        
        return False

    def order(self):
        """优化的下单方法 - 保持原有逻辑不变"""
        if self.success:  # 如果已经成功，不再继续请求
            return 1

        # 保持原有URL和参数不变
        url = ("https://backward.bol.wo.cn/prod-api/promotion/activity/roll/receiveRights?yGdtco4r=0OHXjiGEqWtdwrYv4iwSbrTXqayPd0cVd22KP.hsplVnsy2NGqp5M2NzpIjOJP9t_zwlH.u7mpEmAOBiG4LSbEYJPgS0b5dZEoYTNIfw1gK4DmdKeljHAtyJOeJ5zo_bwc_hfJtADHzhagZUBxKpYoubO_Chheqf0z6TN__9Ynl5YbxQlezYyA58aRBwfFRCYGr_CGnWdpejfyKplHHGDPeR5gQ4qkRBC78N7gg7_ZuegrxGZKB7BeK1by4aDmAYbZZIDBA1InZWqtq8VDT90JTjRuePoRvAxa1dfhUAw6m0dorxrnFMqC0ZvHIVzS7UyDeBn7QZvTdUqY9aTtYjP8DAQHSmw7CH6LnKaftizEE6yMcXK0tRiSohSmIRlzWfn.VMhdFCp.Llbq4IXJdzVWRh1jgcVj3AvF4u3u0O7rUhax8kCSRkdVT5uujKakCGyxwIKslJuzyxlZl8Uo2x4.Ig8Iu.DCyybEQxDuu6dpkw1HIIt1rVpr5IYn4zx1AtvFuG3QY8Jjzr1")

        # 保持原有headers不变
        headers = {
            "Authorization": f"Bearer {self.bearer}",
            "Content-Type": "application/json",
            "User-Agent": CFG.UA,
            "Origin": "https://contact.bol.wo.cn",
            "Referer": "https://contact.bol.wo.cn",
        }

        # 保持原有body不变
        now = datetime.now()
        body = {
            "channelId": None,
            "activityId": CFG.ACTIVITY_ID,
            "productId": CFG.PRODUCT_ID,
            "productCode": CFG.PRODUCT_CODE,
            "currentTime": f"{now.year}-{now.month}-{now.day}",
            "accountType": "1",
            'account':'*********'
        }

        try:
            start_time = time.time()
            r = self.client.post(url, headers=headers, json=body)
            data = r.json()
            response_time = time.time() - start_time

            # 更新统计信息
            self.stats['request_count'] += 1

            code = data.get("code")
            msg = data.get("msg", "")
            log(f"第{self.run_id+1}轮：领取返回 code={code}, msg={msg} - 耗时: {response_time:.3f}s", "info", self.phone)

            # 保持原有成功判断逻辑不变
            if (code == 200 and any(k in msg for k in ["成功", "已兑换", "已领取"])) or \
               (code == 500 and "已参与" in msg):
                log(f"第{self.run_id+1}轮：抢购成功！（已抢到）", "ok", self.phone)
                self.success = True
                return 1
            if "已抢空" in msg or "不足" in msg:
                return 0
            return 0
        except Exception as e:
            log(f"第{self.run_id+1}轮：order 异常: {e}", "err", self.phone)
            return 0

    def run(self):
        """优化的运行方法 - 保持原有逻辑不变"""
        if not self.login():
            return False

        # 优化：高精度时间等待
        now = datetime.now()
        t = datetime.strptime(CFG.START_TIME, "%H:%M:%S.%f")
        target = now.replace(hour=t.hour, minute=t.minute, second=t.second,
                             microsecond=t.microsecond)
        if target < now:
            target += timedelta(days=1)

        wait_sec = (target - now).total_seconds()
        if wait_sec > 0:
            log(f"第{self.run_id+1}轮：等待 {wait_sec:.3f} 秒，到 {CFG.START_TIME}", "info", self.phone)
            # 优化：使用高精度等待
            PrecisionTimer.wait_until_precise(target)

        log(f"第{self.run_id+1}轮：🚀 进入抢购时段！", "ok", self.phone)

        # 保持原有抢购逻辑不变
        for i in range(CFG.MAX_RUSH_TIMES):
            if self.success:  # 如果已经成功，提前退出
                break

            result = self.order()
            if result == 1:
                break

            # 优化：智能延迟控制
            if i < CFG.MAX_RUSH_TIMES - 1:  # 最后一次不延迟
                delay = CFG.SLEEP * (1.2 ** i)  # 轻微递增延迟
                time.sleep(min(delay, 0.1))  # 最大100ms

        if not self.success:
            log(f"第{self.run_id+1}轮：抢购次数已用完，未成功抢购", "warn", self.phone)

        return self.success

# -------------------- 优化的主函数 --------------------
def main():
    """优化的主函数 - 保持原有逻辑不变"""
    tokens = split_tokens()
    if not tokens:
        log("未配置 token", "err")
        return

    # 清理过期缓存
    login_cache.clear_expired()

    log(f"🚀 性能优化版本启动", "info")
    log(f"共 {len(tokens)} 个账号，每个账号运行 {CFG.ACCOUNT_RUN_TIMES} 轮，"
        f"每轮尝试 {CFG.MAX_RUSH_TIMES} 次，线程池大小: {CFG.MAX_WORKERS}", "info")
    log(f"优化功能：连接池复用、登录缓存、高精度时间控制、智能重试", "info")

    start_time = time.time()
    success_count = 0
    total_count = 0

    try:
        # 优化：使用更大的线程池
        with ThreadPoolExecutor(max_workers=CFG.MAX_WORKERS) as executor:
            # 为每个token创建多次任务（根据ACCOUNT_RUN_TIMES配置）
            futures = []
            for tk in tokens:
                for run_id in range(CFG.ACCOUNT_RUN_TIMES):
                    future = executor.submit(RushVip(tk, run_id).run)
                    futures.append(future)

            # 处理结果
            for future in as_completed(futures):
                try:
                    success = future.result()
                    total_count += 1
                    if success:
                        success_count += 1
                        log(f"账号抢购成功", "ok")
                    else:
                        log(f"账号抢购失败", "warn")
                except Exception as e:
                    total_count += 1
                    log(f"账号执行出错: {e}", "err")

    except KeyboardInterrupt:
        log("用户中断程序", "warn")
    except Exception as e:
        log(f"程序异常: {e}", "err")
    finally:
        # 性能统计
        total_time = time.time() - start_time
        success_rate = (success_count / total_count * 100) if total_count > 0 else 0

        log("=" * 50, "info")
        log(f"📊 执行完成统计", "info")
        log(f"总耗时: {total_time:.2f}s", "info")
        log(f"成功率: {success_rate:.1f}% ({success_count}/{total_count})", "info")
        log(f"平均每任务耗时: {total_time/total_count:.3f}s", "info") if total_count > 0 else None
        log(f"缓存命中统计: 可通过日志查看", "info")

        # 清理资源
        connection_manager.close()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东抢购脚本 - 性能优化版本
主要优化：并发性能、网络请求、h5st签名获取、时间精度等
"""

import asyncio
import aiohttp
import time
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Event, Lock, Semaphore
from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from log import logger
import urllib3
from functools import lru_cache
import queue
import threading

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

@dataclass
class PerformanceConfig:
    """性能配置类"""
    # 并发配置
    max_threads: int = 8  # 增加线程数
    max_concurrent_h5st: int = 3  # h5st并发获取限制
    max_concurrent_api: int = 5   # API并发请求限制
    
    # 网络配置
    connection_pool_size: int = 20
    connection_pool_maxsize: int = 50
    request_timeout: int = 3
    connect_timeout: int = 2
    read_timeout: int = 5
    
    # 重试配置
    max_retries: int = 2
    retry_delay: float = 0.1
    
    # 时间配置
    time_sync_precision: float = 0.0001  # 100微秒精度
    rush_prepare_time: int = 3000  # 减少到3秒
    
    # 缓存配置
    h5st_cache_ttl: int = 30  # h5st缓存30秒
    enable_h5st_batch: bool = True  # 启用批量获取h5st

class OptimizedH5STManager:
    """优化的h5st签名管理器"""
    
    def __init__(self, config: PerformanceConfig):
        self.config = config
        self.cache = {}  # 简单内存缓存
        self.cache_lock = Lock()
        self.semaphore = Semaphore(config.max_concurrent_h5st)
        self.batch_queue = queue.Queue()
        self.batch_results = {}
        
    def _is_cache_valid(self, pt_pin: str) -> bool:
        """检查缓存是否有效"""
        if pt_pin not in self.cache:
            return False
        
        cache_time, _ = self.cache[pt_pin]
        return (time.time() - cache_time) < self.config.h5st_cache_ttl
    
    def get_cached_h5st(self, pt_pin: str) -> Optional[str]:
        """获取缓存的h5st"""
        with self.cache_lock:
            if self._is_cache_valid(pt_pin):
                _, h5st = self.cache[pt_pin]
                logger.debug(f"使用缓存h5st: {pt_pin}")
                return h5st
        return None
    
    def cache_h5st(self, pt_pin: str, h5st: str):
        """缓存h5st"""
        with self.cache_lock:
            self.cache[pt_pin] = (time.time(), h5st)
    
    async def get_h5st_async(self, session: aiohttp.ClientSession, pt_pin: str) -> Optional[str]:
        """异步获取h5st签名"""
        # 先检查缓存
        cached = self.get_cached_h5st(pt_pin)
        if cached:
            return cached
            
        with self.semaphore:  # 限制并发数
            try:
                h5st_payload = {
                    "version": "5.2.0",
                    "pin": pt_pin,
                    "ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    "appId": "babelh5",
                    "body": {
                        "functionId": "newBabelAwardCollection",
                        "appid": "babelh5",
                        "body": {"activityId": "3XgsQ4Caupu9ut9814NggDCbjk4L"}
                    }
                }
                
                async with session.post(
                    "http://192.168.241.168:3001/h5st",
                    json=h5st_payload,
                    timeout=aiohttp.ClientTimeout(total=self.config.request_timeout)
                ) as response:
                    if response.status in [200, 201]:
                        result = await response.json()
                        if result.get("code") == 200:
                            h5st = result["body"]["h5st"]["h5st"]
                            self.cache_h5st(pt_pin, h5st)
                            return h5st
                            
            except Exception as e:
                logger.error(f"异步获取h5st失败 {pt_pin}: {e}")
                
        return None

class OptimizedJDRushBuyer:
    """优化的京东抢购类"""
    
    def __init__(self, cookie_str: str, account_id: str, config: PerformanceConfig):
        self.cookie_str = cookie_str
        self.pt_pin = self._extract_pt_pin(cookie_str)
        self.account_id = account_id
        self.config = config
        
        # 优化的session配置
        self.session = self._create_optimized_session()
        
    def _extract_pt_pin(self, cookie_str: str) -> str:
        """提取pt_pin"""
        try:
            for part in cookie_str.split(';'):
                part = part.strip()
                if part.startswith('pt_pin='):
                    return part.split('=')[1]
        except Exception:
            pass
        return "unknown"
    
    def _create_optimized_session(self):
        """创建优化的requests session"""
        import requests
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=self.config.max_retries,
            backoff_factor=self.config.retry_delay,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        # 配置连接池
        adapter = HTTPAdapter(
            pool_connections=self.config.connection_pool_size,
            pool_maxsize=self.config.connection_pool_maxsize,
            max_retries=retry_strategy
        )
        
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置headers
        session.headers.update({
            "Host": "api.m.jd.com",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Connection": "keep-alive",  # 启用keep-alive
            "Cookie": self.cookie_str
        })
        
        session.verify = False
        return session
    
    async def make_request_async(self, h5st_manager: OptimizedH5STManager, 
                                thread_id: str) -> Dict[str, Any]:
        """异步执行请求"""
        start_time = time.time() * 1000
        
        try:
            # 创建异步session
            connector = aiohttp.TCPConnector(
                limit=self.config.connection_pool_size,
                limit_per_host=self.config.connection_pool_maxsize,
                ssl=False
            )
            
            async with aiohttp.ClientSession(connector=connector) as session:
                # 异步获取h5st
                h5st = await h5st_manager.get_h5st_async(session, self.pt_pin)
                
                if not h5st:
                    return {
                        'thread_id': thread_id,
                        'account': self.pt_pin,
                        'error': 'h5st获取失败',
                        'success': False
                    }
                
                # 构建请求数据
                post_data = {
                    'functionId': 'newBabelAwardCollection',
                    'appid': 'babelh5',
                    'body': json.dumps({"activityId": "3XgsQ4Caupu9ut9814NggDCbjk4L"}),
                    'client': 'wh5',
                    'h5st': h5st,
                    'clientVersion': '1.0.0'
                }
                
                # 异步发送请求
                async with session.post(
                    "https://api.m.jd.com/client.action",
                    data=post_data,
                    timeout=aiohttp.ClientTimeout(total=self.config.request_timeout)
                ) as response:
                    response_text = await response.text()
                    end_time = time.time() * 1000
                    
                    logger.info(f"账号[{self.pt_pin}] {thread_id} - 请求完成 - "
                              f"耗时: {end_time - start_time:.2f}ms")
                    
                    return {
                        'thread_id': thread_id,
                        'account': self.pt_pin,
                        'status_code': response.status,
                        'response_time': end_time - start_time,
                        'response_text': response_text[:100],
                        'success': True
                    }
                    
        except Exception as e:
            end_time = time.time() * 1000
            logger.error(f"账号[{self.pt_pin}] {thread_id} - 请求失败: {e}")
            return {
                'thread_id': thread_id,
                'account': self.pt_pin,
                'error': str(e),
                'response_time': end_time - start_time,
                'success': False
            }

class OptimizedRushExecutor:
    """优化的抢购执行器"""
    
    def __init__(self, config: PerformanceConfig):
        self.config = config
        self.h5st_manager = OptimizedH5STManager(config)
        
    async def execute_rush_async(self, buyers: List[OptimizedJDRushBuyer], 
                                rush_time: int) -> Dict[str, Any]:
        """异步执行抢购"""
        logger.info(f"开始异步抢购执行 - 账号数: {len(buyers)}")
        
        start_time = time.time() * 1000
        tasks = []
        
        # 创建异步任务
        for i, buyer in enumerate(buyers):
            for j in range(self.config.max_threads // len(buyers) or 1):
                thread_id = f"{buyer.account_id}-{j+1}"
                task = buyer.make_request_async(self.h5st_manager, thread_id)
                tasks.append(task)
        
        # 并发执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        success_count = sum(1 for r in results if isinstance(r, dict) and r.get('success'))
        total_time = time.time() * 1000 - start_time
        
        return {
            'total_requests': len(results),
            'success_count': success_count,
            'total_time': total_time,
            'results': results
        }

# 使用示例
async def main():
    """主函数示例"""
    config = PerformanceConfig(
        max_threads=16,  # 增加并发数
        max_concurrent_h5st=5,
        connection_pool_size=30
    )
    
    cookies = [
        "pt_key=xxx; pt_pin=account1;",
        "pt_key=yyy; pt_pin=account2;",
    ]
    
    buyers = [
        OptimizedJDRushBuyer(cookie, f"buyer_{i}", config)
        for i, cookie in enumerate(cookies)
    ]
    
    executor = OptimizedRushExecutor(config)
    rush_time = int(time.time() * 1000) + 5000  # 5秒后执行
    
    # 等待到抢购时间
    while time.time() * 1000 < rush_time:
        await asyncio.sleep(0.001)
    
    # 执行抢购
    results = await executor.execute_rush_async(buyers, rush_time)
    
    logger.info(f"抢购完成 - 成功: {results['success_count']}/{results['total_requests']}")
    logger.info(f"总耗时: {results['total_time']:.2f}ms")

if __name__ == "__main__":
    asyncio.run(main())

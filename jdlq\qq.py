#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
联通→迅雷超级会员月卡抢购（支持线程池、次数配置及账号追踪）
逻辑：使用线程池并发抢购，日志中包含账号信息，方便查看每个账号的请求情况
"""

import os, re, json, time, ssl, certifi
import httpx
from dataclasses import dataclass
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed

# -------------------- 工具函数 --------------------
ssl_context = ssl.create_default_context(cafile=certifi.where())
ssl_context.set_ciphers('DEFAULT@SECLEVEL=1')

def log(msg, level="info", phone=None):
    colors = {"info": "\033[0m", "ok": "\033[92m", "warn": "\033[93m", "err": "\033[91m"}
    prefix = {"info": "ℹ️", "ok": "✅", "warn": "⚠️", "err": "❌"}.get(level, "ℹ️")
    # 增加账号信息显示，隐藏中间4位保护隐私
    phone_str = f"[{phone[:3]}****{phone[-4:]}] " if phone else ""
    print(f"{colors[level]}[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] {prefix} {phone_str}{msg}\033[0m")

def env(key, default=""):
    return os.environ.get(key, default).strip()

def split_tokens():
    # ⚠️ 改成你自己的 token
    raw = "d83160d259876a0c927cdaa36c1b4eca79fcaa20560f587f9b85327d7012904a5c04187ac719d3fd7ac2054682a7632f59aafdc2faae23e156e1eea5a7c780db77cb700a5548b94035d3086d890e254b55d230ac59402ab68d36bb42da87b5042fddcb677ad6d36134b7fea23de3150505fca8f12253d6d30f94fa6f637739b92cbf8c89e8df31cde20efa7bf82b9ffb0c37548d71bfb34cf6f533db161f76ded34cd8ff99510ba7409381e91b737b42c602f332b12581670b93335f279e8d4778c00e21bf3811589f2d22148c702fe9b4433fbcbb39293f5fdb716d1c47637cc8dbb4103ae077ae26a7f6bc97ec60559468ad4acb6b24ab1ad7f7f4f98c456b265c914065158cf72c170570d9cafe90698dc52669adb68533857f8fbdfe5e430b16606bbaa416d405136be63d0863fa&8c044285444c99a59419b615c8ed191636e5fd7a2ca497f94462cd13a93da56ab2bced74cd0b40f6bc8acb92ed3a13aef75a6d8513978032efa07aca2ddd47f9199ae462543a8069240cb13c6601227a80b9dd822e6424b7114645689e3a29a8dd0eb2b1bdceaa26ab5c135a6e532c41f246f26ca7605fbda2e76e564796651154e4fbddf2fdb8c9980e4a2768ae011f4143cdf4931d9ad17749b3e61dd8fb4a74c312bbb78cf5b775f34df5d0aea14198364fbeae68aa021fff7a098317c5b40f02778e551026b3edc528c953575d8ce47a4e7e53b5d83d9f33745b3082ba4285b6fc8466e9f4fb0479bb9e3e2b7b32442db4e71a5529671b09b0106050309c06cdc8650942a5478d6e53959bb065c0dfa86dbb7a8b7948eea9b089754700ff3434135acc8dfbf77095de192eae9c87"
    return re.split(r'[\n&^@%]+', raw.strip()) if raw else []

# -------------------- 配置区 --------------------
@dataclass
class Config:
    UA: str = env("MOBILE_UA") or (
        "Mozilla/5.0 (Linux; Android 11; Mi 10) "
        "AppleWebKit/537.36 Chrome/87.0.4280.141 Mobile Safari/537.36"
    )
    SLEEP: float = float(env("RUSH_SLEEP") or "0.01")  # 每次请求间隔时间(秒)
    START_TIME: str = env("START_TIME") or "8:44:59.000"  # 支持毫秒，例如 09:59:59.500
    MAX_RUSH_TIMES: int = int(env("MAX_RUSH_TIMES") or "10")  # 最大抢购次数
    MAX_WORKERS: int = int(env("MAX_WORKERS") or "4")  # 线程池最大线程数
    PRODUCT_CODE: str = "1000011017"
    ACTIVITY_CODE: str = "YOUCHOICEONE"
    ACTIVITY_ID: int = 11
    PRODUCT_ID: int = 271

CFG = Config()

# -------------------- 业务类 --------------------
class RushVip:
    def __init__(self, token):
        self.token  = token.strip()
        self.phone  = None  # 保存手机号
        self.bearer = None
        self.client = httpx.Client(verify=ssl_context, timeout=30)
        self.success = False  # 标记是否成功抢购

    def close(self):
        self.client.close()

    def login(self):
        try:
            r1 = self.client.post(
                "https://m.client.10010.com/mobileService/onLine.htm",
                data={"isFirstInstall": "1", "version": "android@11.0702", "token_online": self.token}
            )
            ecs = r1.json().get("ecs_token")
            if not ecs:
                log("ecs_token 获取失败", "err"); return False

            r2 = self.client.get(
                "https://m.client.10010.com/mobileService/openPlatform/openPlatLineNew.htm",
                params={"to_url": "https://contact.bol.wo.cn/market"},
                headers={"Cookie": f"ecs_token={ecs}"}, follow_redirects=False
            )
            ticket = r2.headers.get("Location", "").split("ticket=")[-1].split("&")[0]
            if not ticket:
                log("ticket 获取失败", "err"); return False

            r3 = self.client.post(
                f"https://backward.bol.wo.cn/prod-api/auth/marketUnicomLogin?ticket={ticket}"
            )
            self.bearer = r3.json().get("data", {}).get("token")
            self.phone  = r1.json().get("desmobile")

            if self.bearer:
                log("登录成功", "ok", self.phone)
                return True
            return False
        except Exception as e:
            log(f"登录异常: {e}", "err", self.phone)
            return False

    def order(self):
        if self.success:  # 如果已经成功，不再继续请求
            return 1
            
        url = ("https://backward.bol.wo.cn/prod-api/promotion/activity/roll/receiveRights?yGdtco4r=0OHXjiGEqWtdwrYv4iwSbrTXqayPd0cVd22KP.hsplVnsy2NGqp5M2NzpIjOJP9t_zwlH.u7mpEmAOBiG4LSbEYJPgS0b5dZEoYTNIfw1gK4DmdKeljHAtyJOeJ5zo_bwc_hfJtADHzhagZUBxKpYoubO_Chheqf0z6TN__9Ynl5YbxQlezYyA58aRBwfFRCYGr_CGnWdpejfyKplHHGDPeR5gQ4qkRBC78N7gg7_ZuegrxGZKB7BeK1by4aDmAYbZZIDBA1InZWqtq8VDT90JTjRuePoRvAxa1dfhUAw6m0dorxrnFMqC0ZvHIVzS7UyDeBn7QZvTdUqY9aTtYjP8DAQHSmw7CH6LnKaftizEE6yMcXK0tRiSohSmIRlzWfn.VMhdFCp.Llbq4IXJdzVWRh1jgcVj3AvF4u3u0O7rUhax8kCSRkdVT5uujKakCGyxwIKslJuzyxlZl8Uo2x4.Ig8Iu.DCyybEQxDuu6dpkw1HIIt1rVpr5IYn4zx1AtvFuG3QY8Jjzr1")
        headers = {
            "Authorization": f"Bearer {self.bearer}",
            "Content-Type": "application/json",
            "User-Agent": CFG.UA,
            "Origin": "https://contact.bol.wo.cn",
            "Referer": "https://contact.bol.wo.cn",
        }
        now = datetime.now()
        body = {
            "channelId": None,
            "activityId": CFG.ACTIVITY_ID,
            "productId": CFG.PRODUCT_ID,
            "productCode": CFG.PRODUCT_CODE,
            "currentTime": f"{now.year}-{now.month}-{now.day}",
            "accountType": "1",
            'account':'*********'
        }
        try:
            r = self.client.post(url, headers=headers, json=body)
            data = r.json()
            code = data.get("code")
            msg  = data.get("msg", "")
            log(f"领取返回 code={code}, msg={msg}", "info", self.phone)

            if (code == 200 and any(k in msg for k in ["成功", "已兑换", "已领取"])) or \
               (code == 500 and "已参与" in msg):
                log("抢购成功！（已抢到）", "ok", self.phone)
                self.success = True
                return 1
            if "已抢空" in msg or "不足" in msg:
                return 0
            return 0
        except Exception as e:
            log(f"order 异常: {e}", "err", self.phone)
            return 0

    def run(self):
        if not self.login():
            return False

        # 🚀 精准等待到毫秒
        now = datetime.now()
        t = datetime.strptime(CFG.START_TIME, "%H:%M:%S.%f")
        target = now.replace(hour=t.hour, minute=t.minute, second=t.second,
                             microsecond=t.microsecond)
        if target < now:
            target += timedelta(days=1)

        wait_sec = (target - now).total_seconds()
        if wait_sec > 0:
            log(f"等待 {wait_sec:.3f} 秒，到 {CFG.START_TIME}", "info", self.phone)
            start = time.perf_counter()
            while True:
                if (time.perf_counter() - start) >= wait_sec:
                    break

        log("🚀 进入抢购时段！", "ok", self.phone)
        
        # 执行指定次数的抢购
        for i in range(CFG.MAX_RUSH_TIMES):
            if self.success:  # 如果已经成功，提前退出
                break
                
            result = self.order()
            if result == 1:
                break
                
            # 控制请求频率
            time.sleep(CFG.SLEEP)
        
        if not self.success:
            log("抢购次数已用完，未成功抢购", "warn", self.phone)
            
        return self.success

# -------------------- main --------------------
def main():
    tokens = split_tokens()
    if not tokens:
        log("未配置 token", "err"); return
    log(f"共 {len(tokens)} 个账号，线程池大小: {CFG.MAX_WORKERS}，最大尝试次数: {CFG.MAX_RUSH_TIMES}", "info")

    # 创建线程池
    with ThreadPoolExecutor(max_workers=CFG.MAX_WORKERS) as executor:
        # 为每个token创建一个任务
        futures = {executor.submit(RushVip(tk).run): tk for tk in tokens}
        
        # 处理结果
        for future in as_completed(futures):
            token = futures[future]
            try:
                success = future.result()
                # 从token对应的实例中获取手机号（这里简化处理，实际可通过其他方式关联）
                if success:
                    log(f"账号抢购成功", "ok")
                else:
                    log(f"账号抢购失败", "warn")
            except Exception as e:
                log(f"账号执行出错: {e}", "err")

if __name__ == "__main__":
    main()

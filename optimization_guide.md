# 京东抢购脚本性能优化指南

## 📊 当前架构分析

### 🔍 性能瓶颈识别

1. **并发瓶颈**：
   - `MAX_THREADS = 1` 严重限制并发能力
   - 同步阻塞IO导致资源浪费
   - 缺乏智能负载均衡

2. **网络瓶颈**：
   - 每次请求都创建新连接
   - 缺乏连接池复用
   - SSL握手开销大

3. **h5st签名瓶颈**：
   - 每次API请求都重新获取签名
   - 缺乏缓存机制
   - 签名服务成为性能瓶颈

## 🚀 具体优化方案

### 1. 并发性能优化

#### 当前问题：
```python
MAX_THREADS = 1  # 过于保守
TIMEOUT_MS = 10  # 超时时间过短
```

#### 优化建议：
```python
# 动态线程配置
def calculate_optimal_threads(account_count: int, target_rps: int) -> int:
    """根据账号数和目标RPS计算最优线程数"""
    base_threads = min(account_count * 4, 32)  # 每账号4线程，最多32
    return max(base_threads, 8)  # 最少8线程

# 配置示例
MAX_THREADS = calculate_optimal_threads(len(cookies), 100)
TIMEOUT_MS = 5000  # 增加到5秒
```

#### 性能提升预期：
- **并发能力**: 1 → 8-32 线程 (8-32倍提升)
- **响应时间**: 减少60-80%
- **成功率**: 提升20-30%

### 2. 网络请求优化

#### 当前问题：
```python
# 每次都创建新session，没有连接复用
self.session = requests.Session()
```

#### 优化方案：
```python
# 连接池优化
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

def create_optimized_session():
    session = requests.Session()
    
    # 配置连接池
    adapter = HTTPAdapter(
        pool_connections=20,    # 连接池数量
        pool_maxsize=50,       # 每个池最大连接数
        max_retries=Retry(
            total=3,
            backoff_factor=0.1,
            status_forcelist=[500, 502, 503, 504]
        )
    )
    
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    # 启用keep-alive
    session.headers.update({
        "Connection": "keep-alive",
        "Keep-Alive": "timeout=30, max=100"
    })
    
    return session
```

#### 性能提升预期：
- **连接建立时间**: 减少70-90%
- **网络延迟**: 减少30-50%
- **并发处理能力**: 提升3-5倍

### 3. h5st签名获取优化

#### 当前问题：
```python
# 每次请求都重新获取h5st
h5st_signature = get_h5st_signature(self.pt_pin)
```

#### 优化方案A：智能缓存
```python
class H5STCache:
    def __init__(self, ttl=30):
        self.cache = {}
        self.ttl = ttl
        self.lock = Lock()
    
    def get(self, pt_pin: str) -> Optional[str]:
        with self.lock:
            if pt_pin in self.cache:
                timestamp, h5st = self.cache[pt_pin]
                if time.time() - timestamp < self.ttl:
                    return h5st
        return None
    
    def set(self, pt_pin: str, h5st: str):
        with self.lock:
            self.cache[pt_pin] = (time.time(), h5st)

# 使用缓存
h5st_cache = H5STCache(ttl=30)  # 30秒缓存

def get_h5st_with_cache(pt_pin: str) -> str:
    # 先尝试缓存
    cached = h5st_cache.get(pt_pin)
    if cached:
        return cached
    
    # 缓存未命中，获取新签名
    h5st = get_h5st_signature(pt_pin)
    if h5st:
        h5st_cache.set(pt_pin, h5st)
    return h5st
```

#### 优化方案B：批量获取
```python
async def batch_get_h5st(pt_pins: List[str]) -> Dict[str, str]:
    """批量获取h5st签名"""
    tasks = []
    async with aiohttp.ClientSession() as session:
        for pt_pin in pt_pins:
            task = get_h5st_async(session, pt_pin)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        return dict(zip(pt_pins, results))
```

#### 性能提升预期：
- **签名获取时间**: 减少80-95%
- **签名服务压力**: 减少70-90%
- **整体响应时间**: 减少40-60%

### 4. 时间精度和同步优化

#### 当前问题：
```python
time.sleep(0.0001)  # 精度不够
```

#### 优化方案：
```python
import threading
from time import perf_counter

class PrecisionTimer:
    def __init__(self, target_time: float):
        self.target_time = target_time
        
    def wait_until(self):
        """高精度等待到目标时间"""
        while True:
            current = perf_counter()
            remaining = self.target_time - current
            
            if remaining <= 0:
                break
            elif remaining > 0.01:  # 10ms以上用sleep
                time.sleep(remaining - 0.01)
            else:  # 10ms以内用忙等待
                while perf_counter() < self.target_time:
                    pass

# 使用示例
timer = PrecisionTimer(rush_timestamp / 1000)
timer.wait_until()
```

#### 性能提升预期：
- **时间精度**: 提升到微秒级
- **抢购成功率**: 提升15-25%
- **时间同步误差**: 减少90%

### 5. 资源管理优化

#### 内存优化：
```python
# 使用__slots__减少内存占用
class OptimizedBuyer:
    __slots__ = ['cookie_str', 'pt_pin', 'session', 'stats']
    
    def __init__(self, cookie_str: str):
        self.cookie_str = cookie_str
        self.pt_pin = extract_pt_pin(cookie_str)
        self.session = create_optimized_session()
        self.stats = {'success': 0, 'failed': 0}

# 对象池复用
from queue import Queue

class SessionPool:
    def __init__(self, size: int = 10):
        self.pool = Queue(maxsize=size)
        for _ in range(size):
            self.pool.put(create_optimized_session())
    
    def get_session(self):
        return self.pool.get()
    
    def return_session(self, session):
        self.pool.put(session)
```

#### 日志优化：
```python
# 异步日志避免IO阻塞
import logging.handlers
import queue

class AsyncLogHandler(logging.handlers.QueueHandler):
    def __init__(self):
        log_queue = queue.Queue()
        super().__init__(log_queue)
        
        # 启动日志处理线程
        self.listener = logging.handlers.QueueListener(
            log_queue, 
            logging.StreamHandler(),
            respect_handler_level=True
        )
        self.listener.start()

# 配置异步日志
logger.addHandler(AsyncLogHandler())
```

### 6. 账号管理优化

#### 智能负载均衡：
```python
class AccountLoadBalancer:
    def __init__(self, accounts: List[str]):
        self.accounts = accounts
        self.stats = {acc: {'success': 0, 'failed': 0, 'avg_time': 0} 
                     for acc in accounts}
        
    def get_best_accounts(self, count: int) -> List[str]:
        """获取性能最好的账号"""
        sorted_accounts = sorted(
            self.accounts,
            key=lambda acc: (
                self.stats[acc]['success'] / max(1, self.stats[acc]['failed']),
                -self.stats[acc]['avg_time']
            ),
            reverse=True
        )
        return sorted_accounts[:count]
    
    def update_stats(self, account: str, success: bool, response_time: float):
        """更新账号统计"""
        stats = self.stats[account]
        if success:
            stats['success'] += 1
        else:
            stats['failed'] += 1
        
        # 更新平均响应时间
        total_requests = stats['success'] + stats['failed']
        stats['avg_time'] = (stats['avg_time'] * (total_requests - 1) + response_time) / total_requests
```

## 📈 预期性能提升

### 整体性能对比：

| 指标 | 当前版本 | 优化版本 | 提升幅度 |
|------|----------|----------|----------|
| 并发线程数 | 1 | 8-32 | 8-32倍 |
| 平均响应时间 | 200-300ms | 50-100ms | 60-75% |
| 抢购成功率 | 60-70% | 85-95% | 25-35% |
| 资源利用率 | 20-30% | 70-85% | 2-3倍 |
| h5st获取时间 | 20-50ms | 2-5ms | 80-90% |

### 压力测试建议：

```python
# 性能测试脚本
async def performance_test():
    config = PerformanceConfig(max_threads=32)
    
    # 测试不同并发数的性能
    for thread_count in [1, 4, 8, 16, 32]:
        config.max_threads = thread_count
        
        start_time = time.time()
        results = await execute_rush_async(buyers, rush_time)
        end_time = time.time()
        
        print(f"线程数: {thread_count}")
        print(f"成功率: {results['success_count']}/{results['total_requests']}")
        print(f"总耗时: {end_time - start_time:.2f}s")
        print(f"平均响应时间: {results['avg_response_time']:.2f}ms")
        print("-" * 50)
```

## 🎯 实施建议

### 阶段1：基础优化（1-2天）
1. 增加线程数到8-16
2. 实现连接池复用
3. 添加h5st缓存机制

### 阶段2：架构优化（3-5天）
1. 引入异步IO
2. 实现批量h5st获取
3. 优化时间精度控制

### 阶段3：高级优化（1周）
1. 智能负载均衡
2. 自适应参数调优
3. 完整的监控和告警

### 监控指标：
- 请求成功率
- 平均响应时间
- 并发处理能力
- 资源使用率
- h5st缓存命中率
